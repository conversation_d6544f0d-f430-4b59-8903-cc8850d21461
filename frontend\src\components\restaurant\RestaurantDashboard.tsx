import React, { useState, useEffect, createContext, useContext } from "react";
import {
  Routes,
  Route,
  Navigate,
  Link,
  useNavigate,
  useLocation,
  useParams,
} from "react-router-dom";
import { motion } from "framer-motion";
import {
  BarChart3,
  Music,
  Users,
  Settings,
  PlayCircle,
  TrendingUp,
  Clock,
  Star,
  ThumbsUp,
  Play,
  LogOut,
  User,
  QrCode,
  AlertCircle,
} from "lucide-react";

// Components
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { toast } from "react-hot-toast";
import { useAuth } from "@/store";
import apiService from "@/services/api";

// Restaurant-specific components
import SuggestionsManager from "@/components/restaurant/SuggestionsManager";
import PlaybackController from "@/components/restaurant/PlaybackController";
import PlaylistManager from "@/components/restaurant/PlaylistManager";
import MusicPlayer from "@/components/restaurant/MusicPlayer";
import UnifiedAnalytics from "@/components/restaurant/UnifiedAnalytics";
import GenreManager from "@/components/restaurant/GenreManager";
import RestaurantProfile from "@/components/restaurant/RestaurantProfile";
import QRCodeManager from "@/components/restaurant/QRCodeManager";
import ProblematicTracksAlert from "@/components/restaurant/ProblematicTracksAlert";
import EnhancedRestaurantProfile from "@/components/restaurant/EnhancedRestaurantProfile";

// Temporary admin components
import AdvancedModeration from "@/components/admin/AdvancedModeration";
import RestaurantSettings from "@/components/admin/RestaurantSettings";
import QueueManager from "@/components/admin/QueueManager";

// Types
interface RestaurantContextType {
  restaurantId: string;
}

interface DashboardStats {
  totalSuggestions: number;
  totalVotes: number;
  pendingSuggestions: number;
  dailyStats: { suggestions: number; votes: number };
  totalPlays: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
}

interface Activity {
  id: string;
  title: string;
  artist: string;
  createdAt: string;
  upvotes?: number;
}

interface QueueItem {
  id: string;
  title: string;
  artist: string;
  upvotes: number;
  downvotes: number;
}

// Restaurant Context
const RestaurantContext = createContext<RestaurantContextType>({
  restaurantId: "",
});

export const useRestaurantContext = () => useContext(RestaurantContext);

// Dashboard Home Component
const DashboardHome: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalSuggestions: 0,
    totalVotes: 0,
    pendingSuggestions: 0,
    dailyStats: { suggestions: 0, votes: 0 },
    totalPlays: 0,
    activeUsers: 0,
    averageRating: 0,
    growthRate: 0,
    peakHour: "0:00",
    topGenre: "N/A",
  });
  const [recentActivity, setRecentActivity] = useState<Activity[]>([]);
  const [currentQueue, setCurrentQueue] = useState<QueueItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [dataLoadedOnce, setDataLoadedOnce] = useState<boolean>(false);
  const { restaurantId } = useRestaurantContext();

  useEffect(() => {
    if (!dataLoadedOnce && restaurantId) {
      loadDashboardData();
    }
  }, [restaurantId, dataLoadedOnce]);

  const loadDashboardData = async () => {
    if (loading) return;

    setLoading(true);
    try {
      // Parallel API calls for better performance
      const [statsData, suggestionsData, queueData] = await Promise.all([
        apiService.getAnalytics(restaurantId).catch(() => ({
          summary: null,
        })),
        apiService
          .getSuggestions(restaurantId, { limit: 4 })
          .catch(() => ({ suggestions: [] })),
        apiService.getPlayQueue(restaurantId).catch(() => ({ queue: [] })),
      ]);

      // Update stats
      setStats({
        totalSuggestions: statsData.summary?.totalSuggestions || 0,
        totalVotes: statsData.summary?.totalVotes || 0,
        pendingSuggestions: statsData.summary?.pendingSuggestions || 0,
        dailyStats: {
          suggestions: statsData.summary?.dailySuggestions || 0,
          votes: statsData.summary?.dailyVotes || 0,
        },
        totalPlays: statsData.summary?.totalPlays || 0,
        activeUsers: statsData.summary?.activeUsers || 0,
        averageRating: statsData.summary?.averageRating || 0,
        growthRate: statsData.summary?.growthRate || 0,
        peakHour: statsData.summary?.peakHour || "0:00",
        topGenre: statsData.summary?.topGenre || "N/A",
      });

      // Update recent activity
      setRecentActivity(
        suggestionsData.suggestions?.length
          ? suggestionsData.suggestions
          : [
              {
                id: "demo-1",
                title: "Exemplo de Música",
                artist: "Artista Demo",
                createdAt: new Date().toISOString(),
                upvotes: 0,
              },
            ]
      );

      // Update queue
      setCurrentQueue(
        queueData.queue?.length
          ? queueData.queue.slice(0, 4)
          : [
              {
                id: "queue-demo-1",
                title: "Música na Fila",
                artist: "Artista",
                upvotes: 5,
                downvotes: 1,
              },
            ]
      );

      setDataLoadedOnce(true);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      toast.error("Erro ao carregar dados do dashboard");
    } finally {
      setLoading(false);
    }
  };

  const dashboardStats = [
    {
      title: "Sugestões Hoje",
      value: stats.dailyStats.suggestions.toString(),
      change: "+12%",
      icon: Music,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
      description: "Novas sugestões recebidas hoje",
    },
    {
      title: "Total de Votos",
      value: stats.totalVotes.toString(),
      change: `+${stats.dailyStats.votes}`,
      icon: ThumbsUp,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/20",
      description: "Votos acumulados de clientes",
    },
    {
      title: "Pendentes",
      value: stats.pendingSuggestions.toString(),
      change: "0%",
      icon: Clock,
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
      description: "Aguardando aprovação",
    },
    {
      title: "Total Sugestões",
      value: stats.totalSuggestions.toString(),
      change: "+0.2",
      icon: TrendingUp,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
      description: "Sugestões acumuladas",
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Dashboard do Restaurante</h1>
            <p className="text-blue-100 text-lg">
              Visão geral das atividades e estatísticas em tempo real
            </p>
            <div className="flex items-center space-x-4 mt-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-blue-100">Sistema Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-blue-200" />
                <span className="text-sm text-blue-100">
                  Atualizado: {new Date().toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" })}
                </span>
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
              <BarChart3 className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                  <div className={`w-10 h-10 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                    <stat.icon className={`w-5 h-5 ${stat.color}`} />
                  </div>
                </div>
                <p className="text-3xl font-bold text-gray-900 dark:text-white mb-1">{stat.value}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">{stat.description}</p>
                <div className="flex items-center">
                  <span
                    className={`text-sm font-medium ${
                      stat.change.startsWith("+") ? "text-green-600 dark:text-green-400" : "text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    {stat.change}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">hoje</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Problematic Tracks Alert */}
      <ProblematicTracksAlert />

      {/* Activity Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Atividade Recente</h3>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-500 dark:text-gray-400">Ao vivo</span>
            </div>
          </div>
          <div className="space-y-3">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
                    <Music className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      Nova sugestão: "{activity.title}"
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{activity.artist}</p>
                      <span className="text-xs text-gray-400">•</span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.createdAt).toLocaleTimeString("pt-BR", {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <ThumbsUp className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-gray-500">{activity.upvotes || 0}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Music className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">Nenhuma atividade recente</p>
                <p className="text-xs text-gray-400 mt-1">As sugestões dos clientes aparecerão aqui</p>
              </div>
            )}
          </div>
        </div>

        {/* Current Queue */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Fila Atual</h3>
            <div className="flex items-center space-x-2">
              <Play className="w-4 h-4 text-green-500" />
              <span className="text-xs text-gray-500 dark:text-gray-400">{currentQueue.length} músicas</span>
            </div>
          </div>
          <div className="space-y-3">
            {currentQueue.length > 0 ? (
              currentQueue.map((item, i) => (
                <div
                  key={item.id}
                  className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                    i === 0
                      ? "bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700"
                      : "bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full text-xs flex items-center justify-center font-bold ${
                      i === 0
                        ? "bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-sm"
                        : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    {i === 0 ? <Play className="w-3 h-3" /> : i + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p
                      className={`text-sm font-medium truncate ${
                        i === 0 ? "text-gray-900 dark:text-white font-semibold" : "text-gray-900 dark:text-white"
                      }`}
                    >
                      {item.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{item.artist}</p>
                      <span className="text-xs text-gray-400">•</span>
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="w-3 h-3 text-green-500" />
                        <span className="text-xs text-gray-500">{item.upvotes - item.downvotes}</span>
                      </div>
                    </div>
                  </div>
                  {i === 0 && (
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">Tocando</span>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Play className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">Fila vazia</p>
                <p className="text-xs text-gray-400 mt-1">As músicas aprovadas aparecerão aqui</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Restaurant Dashboard Component
const RestaurantDashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const { user, setUser, setAuthToken } = useAuth();
  const [restaurantName] = useState<string>("Restaurante Demo");

  // Validate restaurantId
  if (!restaurantId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Erro de Rota</h1>
          <p className="text-gray-600 mt-2">ID do restaurante não fornecido na URL</p>
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    setUser(null);
    setAuthToken(null);
    localStorage.removeItem("authToken");
    toast.success("Logout realizado com sucesso!");
    navigate("/");
  };

  useEffect(() => {
    console.log("🏪 RestaurantDashboard carregado!");
    console.log("🏪 RestaurantId dinâmico:", restaurantId);
    console.log("🏪 Location atual:", location.pathname);
  }, [location.pathname, restaurantId]);

  return (
    <RestaurantContext.Provider value={{ restaurantId }}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link
                to={`/restaurant/${restaurantId}/dashboard`}
                className="flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer"
                title="Voltar ao Dashboard Principal"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">{restaurantName}</h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Dashboard de Gerenciamento</p>
                </div>
              </Link>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">{restaurantName}</div>
                <div className="flex items-center space-x-2">
                  <Link
                    to="profile"
                    className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer"
                    title="Perfil do Restaurante"
                  >
                    <User className="w-4 h-4 text-white" />
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium"
                    title="Sair"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Sair</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation */}
        <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto">
              {[
                { name: "Player", icon: PlayCircle, path: `/restaurant/${restaurantId}/dashboard/player` },
                { name: "Controle de Reprodução", icon: Settings, path: `/restaurant/${restaurantId}/dashboard/playback-control` },
                { name: "Playlists", icon: Music, path: `/restaurant/${restaurantId}/dashboard/playlists` },
                { name: "Gêneros", icon: Star, path: `/restaurant/${restaurantId}/dashboard/genres` },
                { name: "Analytics", icon: BarChart3, path: `/restaurant/${restaurantId}/dashboard/analytics` },
                { name: "QR Code", icon: QrCode, path: `/restaurant/${restaurantId}/dashboard/qrcode` },
                { name: "Fila", icon: Clock, path: `/restaurant/${restaurantId}/dashboard/queue` },
                { name: "Sugestões", icon: TrendingUp, path: `/restaurant/${restaurantId}/dashboard/suggestions` },
                { name: "Moderação", icon: AlertCircle, path: `/restaurant/${restaurantId}/dashboard/moderation` },
                { name: "Configurações", icon: Settings, path: `/restaurant/${restaurantId}/dashboard/settings` },
              ].map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${
                    location.pathname === item.path
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
                  }`}
                >
                  <item.icon className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Routes>
            <Route path="/" element={<DashboardHome />} />
            <Route path="/player" element={<MusicPlayer />} />
            <Route path="/playback-control" element={<PlaybackController />} />
            <Route path="/playlists" element={<PlaylistManager />} />
            <Route path="/genres" element={<GenreManager />} />
            <Route path="/suggestions" element={<SuggestionsManager />} />
            <Route path="/moderation" element={<AdvancedModeration />} />
            <Route path="/qrcode" element={<QRCodeManager />} />
            <Route path="/queue" element={<QueueManager />} />
            <Route path="/analytics" element={<UnifiedAnalytics />} />
            <Route path="/settings" element={<RestaurantSettings />} />
            <Route path="/profile" element={<RestaurantProfile />} />
            <Route path="/enhanced-profile" element={<EnhancedRestaurantProfile />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </main>
      </div>
    </RestaurantContext.Provider>
  );
};

export default RestaurantDashboard;