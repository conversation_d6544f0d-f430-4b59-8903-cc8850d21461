/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { adexchangebuyer2_v2beta1 } from './v2beta1';
export declare const VERSIONS: {
    v2beta1: typeof adexchangebuyer2_v2beta1.Adexchangebuyer2;
};
export declare function adexchangebuyer2(version: 'v2beta1'): adexchangebuyer2_v2beta1.Adexchangebuyer2;
export declare function adexchangebuyer2(options: adexchangebuyer2_v2beta1.Options): adexchangebuyer2_v2beta1.Adexchangebuyer2;
declare const auth: AuthPlus;
export { auth };
export { adexchangebuyer2_v2beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
