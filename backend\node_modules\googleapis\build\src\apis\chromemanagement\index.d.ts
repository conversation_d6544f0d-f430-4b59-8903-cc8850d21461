/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { chromemanagement_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof chromemanagement_v1.Chromemanagement;
};
export declare function chromemanagement(version: 'v1'): chromemanagement_v1.Chromemanagement;
export declare function chromemanagement(options: chromemanagement_v1.Options): chromemanagement_v1.Chromemanagement;
declare const auth: AuthPlus;
export { auth };
export { chromemanagement_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
