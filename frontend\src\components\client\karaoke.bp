'''''
import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Users,
  Heart,
  Star,
  X,
  <PERSON>otateCcw,
  Settings,
  Maximize,
  Minimize,
} from "lucide-react";
import { toast } from "react-hot-toast";
import YouTube, { YouTubeProps } from "react-youtube";

interface LyricsLine {
  time: number;
  text: string;
  duration?: number;
}

interface LyricsData {
  id: string;
  title: string;
  artist: string;
  duration: number;
  language: string;
  lines: LyricsLine[];
  hasTimestamps: boolean;
}

interface KaraokePlayerProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    id: string;
    title: string;
    artist: string;
    thumbnailUrl?: string;
    duration?: number;
    youtubeVideoId?: string;
  };
  sessionId: string;
  onVoteRequest?: () => void;
}

const KaraokePlayer: React.FC<KaraokePlayerProps> = ({
  isOpen,
  onClose,
  suggestion,
  sessionId,
  onVoteRequest,
}) => {
  const [lyrics, setLyrics] = useState<LyricsData | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(24);
  const [highlightColor, setHighlightColor] = useState("#3B82F6");
  const [loading, setLoading] = useState(false);
  const [currentLineIndex, setCurrentLineIndex] = useState(-1);
  const [progress, setProgress] = useState(0);

  const playerRef = useRef<YouTube>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Carregar letras e inicializar player quando o modal é aberto
  useEffect(() => {
    if (isOpen && suggestion) {
      loadLyrics();
    }

    return () => {
      setLyrics(null);
      setCurrentTime(0);
      setCurrentLineIndex(-1);
      setProgress(0);
      setIsPlaying(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isOpen, suggestion]);

  // Sincronizar letras com o tempo do vídeo
  useEffect(() => {
    if (isPlaying && lyrics && playerRef.current) {
      timerRef.current = setInterval(() => {
        const player = playerRef.current?.getInternalPlayer();
        if (player) {
          player.getCurrentTime().then((time: number) => {
            setCurrentTime(time);
            updateCurrentLine(time);
          });
        }
      }, 50); // Aumentar precisão para 50ms
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isPlaying, lyrics]);

  const loadLyrics = async () => {
    try {
      setLoading(true);
      console.log(`🔍 Buscando letras para: ${suggestion.title} - ${suggestion.artist}`);

      const response = await fetch(
        `http://localhost:8001/api/v1/lyrics/search?title=${encodeURIComponent(
          suggestion.title
        )}&artist=${encodeURIComponent(suggestion.artist)}&youtubeVideoId=${
          suggestion.youtubeVideoId || ""
        }`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.lyrics) {
          setLyrics(data.lyrics);
          toast.success("Letras carregadas! 🎤");
        } else {
          throw new Error("Letras não encontradas na resposta");
        }
      } else {
        // Tentar letras de teste como fallback
        const testResponse = await fetch(
          `http://localhost:8001/api/v1/lyrics/test?title=${encodeURIComponent(
            suggestion.title
          )}&artist=${encodeURIComponent(suggestion.artist)}`
        );

        if (testResponse.ok) {
          const testData = await testResponse.json();
          setLyrics(testData.lyrics);
          toast("Usando letras de demonstração 🎵", { icon: "ℹ️" });
        } else {
          throw new Error("Letras de teste não disponíveis");
        }
      }
    } catch (error) {
      console.error("Erro ao carregar letras:", error);
      toast.error("Não foi possível carregar as letras");
    } finally {
      setLoading(false);
    }
  };

  const updateCurrentLine = (time: number) => {
    if (!lyrics) return;

    let newIndex = -1;
    let newProgress = 0;

    for (let i = 0; i < lyrics.lines.length; i++) {
      if (time >= lyrics.lines[i].time) {
        newIndex = i;
      } else {
        break;
      }
    }

    // Calcular progresso na linha atual
    if (newIndex >= 0 && newIndex < lyrics.lines.length - 1) {
      const currentLine = lyrics.lines[newIndex];
      const nextLine = lyrics.lines[newIndex + 1];
      const lineDuration = nextLine.time - currentLine.time;
      const elapsed = time - currentLine.time;
      newProgress = Math.min(1, Math.max(0, elapsed / lineDuration));
    }

    setCurrentLineIndex(newIndex);
    setProgress(newProgress);
  };

  const togglePlay = () => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isPlaying) {
        player.pauseVideo();
      } else {
        player.playVideo();
      }
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isMuted) {
        player.unMute();
      } else {
        player.mute();
      }
    }
    setIsMuted(!isMuted);
  };

  const restart = () => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      player.seekTo(0);
      if (!isPlaying) {
        player.playVideo();
        setIsPlaying(true);
      }
    }
    setCurrentTime(0);
    setCurrentLineIndex(-1);
    setProgress(0);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleYouTubeReady: YouTubeProps['onReady'] = (event) => {
    console.log("✅ YouTube Player pronto");
    event.target.setVolume(isMuted ? 0 : 50);
  };

  const handleYouTubeStateChange: YouTubeProps['onStateChange'] = (event) => {
    if (event.data === YouTube.PlayerState.PLAYING) {
      setIsPlaying(true);
    } else if (event.data === YouTube.PlayerState.PAUSED) {
      setIsPlaying(false);
    } else if (event.data === YouTube.PlayerState.ENDED) {
      setIsPlaying(false);
      setCurrentTime(0);
      setCurrentLineIndex(-1);
      setProgress(0);
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getCurrentLine = (): LyricsLine | null => {
    if (!lyrics || currentLineIndex < 0) return null;
    return lyrics.lines[currentLineIndex] || null;
  };

  const getNextLine = (): LyricsLine | null => {
    if (!lyrics || currentLineIndex < 0) return null;
    return lyrics.lines[currentLineIndex + 1] || null;
  };

  const getContextLines = (): LyricsLine[] => {
    if (!lyrics || currentLineIndex < 0) return [];

    const start = Math.max(0, currentLineIndex - 2);
    const end = Math.min(lyrics.lines.length, currentLineIndex + 3);

    return lyrics.lines.slice(start, end);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div
        ref={containerRef}
        className={`fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 ${
          isFullscreen ? "p-0" : "p-4"
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-black bg-opacity-30 backdrop-blur-sm">
          <div className="flex items-center space-x-4">
            {suggestion.thumbnailUrl && (
              <img
                src={suggestion.thumbnailUrl}
                alt={`Capa de ${suggestion.title} por ${suggestion.artist}`}
                className="w-16 h-16 rounded-lg object-cover"
              />
            )}
            <div>
              <h2 className="text-2xl font-bold text-white">
                {suggestion.title}
              </h2>
              <p className="text-lg text-gray-300">{suggestion.artist}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              aria-label="Abrir configurações"
            >
              <Settings className="w-6 h-6" />
            </button>

            <button
              onClick={toggleFullscreen}
              className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              aria-label={isFullscreen ? "Sair da tela cheia" : "Entrar na tela cheia"}
            >
              {isFullscreen ? (
                <Minimize className="w-6 h-6" />
              ) : (
                <Maximize className="w-6 h-6" />
              )}
            </button>

            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              aria-label="Fechar player de karaokê"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Settings Panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-black bg-opacity-50 backdrop-blur-sm p-4 mx-6 rounded-lg"
            >
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <span className="text-white text-sm">Tamanho:</span>
                  <input
                    type="range"
                    min="16"
                    max="48"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="w-20"
                    aria-label="Ajustar tamanho da fonte"
                  />
                  <span className="text-white text-sm">{fontSize}px</span>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-white text-sm">Cor:</span>
                  <input
                    type="color"
                    value={highlightColor}
                    onChange={(e) => setHighlightColor(e.target.value)}
                    className="w-8 h-8 rounded border-none"
                    aria-label="Escolher cor de destaque"
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className="flex-1 flex flex-col items-center justify-center p-6">
          {suggestion.youtubeVideoId && (
            <div className="w-full max-w-4xl mb-6">
              <YouTube
                videoId={suggestion.youtubeVideoId}
                opts={{
                  width: "100%",
                  height: isFullscreen ? "400" : "300",
                  playerVars: {
                    autoplay: 0,
                    controls: 0,
                    rel: 0,
                    showinfo: 0,
                  },
                }}
                onReady={handleYouTubeReady}
                onStateChange={handleYouTubeStateChange}
                ref={playerRef}
                className="rounded-lg overflow-hidden"
              />
            </div>
          )}

          {loading ? (
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-white text-xl">Carregando letras...</p>
            </div>
          ) : lyrics ? (
            <div className="text-center max-w-4xl w-full">
              {/* Current Line */}
              <motion.div
                key={currentLineIndex}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mb-8"
              >
                <div
                  className="text-white font-bold leading-relaxed mb-4"
                  style={{
                    fontSize: `${fontSize}px`,
                    color: getCurrentLine() ? highlightColor : "white",
                    textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                  }}
                  aria-live="polite"
                >
                  {getCurrentLine()?.text || "🎵 Aguardando..."}
                </div>

                {/* Progress Bar */}
                {getCurrentLine() && (
                  <div className="w-full bg-white bg-opacity-20 rounded-full h-2 mb-4">
                    <div
                      className="h-2 rounded-full transition-all duration-100"
                      style={{
                        width: `${progress * 100}%`,
                        backgroundColor: highlightColor,
                      }}
                      role="progressbar"
                      aria-valuenow={progress * 100}
                      aria-valuemin={0}
                      aria-valuemax={100}
                    />
                  </div>
                )}
              </motion.div>

              {/* Next Line Preview */}
              {getNextLine() && (
                <div
                  className="text-gray-300 opacity-60 mb-8"
                  style={{ fontSize: `${fontSize * 0.8}px` }}
                >
                  Próxima: {getNextLine()?.text}
                </div>
              )}

              {/* Context Lines */}
              <div className="space-y-2 opacity-40">
                {getContextLines().map((line, index) => {
                  const isCurrentLine =
                    lyrics.lines.indexOf(line) === currentLineIndex;
                  if (isCurrentLine) return null;

                  return (
                    <div
                      key={index}
                      className="text-gray-400"
                      style={{ fontSize: `${fontSize * 0.6}px` }}
                    >
                      {line.text}
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center">
              <Mic className="w-24 h-24 text-white opacity-50 mx-auto mb-4" />
              <p className="text-white text-xl mb-4">🎤 Modo Karaokê Ativo!</p>
              <p className="text-gray-300 mb-2">
                Letras sincronizadas não disponíveis.
              </p>
              <p className="text-gray-300">
                Cante junto com o vídeo! 🎵
              </p>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="bg-black bg-opacity-30 backdrop-blur-sm p-6">
          {/* Progress Bar */}
          {lyrics && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-white text-sm mb-2">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(lyrics.duration)}</span>
              </div>
              <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                <div
                  className="bg-white h-2 rounded-full transition-all duration-100"
                  style={{ width: `${(currentTime / lyrics.duration) * 100}%` }}
                  role="progressbar"
                  aria-valuenow={(currentTime / lyrics.duration) * 100}
                  aria-valuemin={0}
                  aria-valuemax={100}
                />
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex items-center justify-center space-x-6">
            <button
              onClick={restart}
              className="p-3 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-colors"
              aria-label="Reiniciar música"
            >
              <RotateCcw className="w-6 h-6" />
            </button>

            <button
              onClick={togglePlay}
              className="p-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              aria-label={isPlaying ? "Pausar música" : "Tocar música"}
            >
              {isPlaying ? (
                <Pause className="w-8 h-8" />
              ) : (
                <Play className="w-8 h-8" />
              )}
            </button>

            <button
              onClick={toggleMute}
              className="p-3 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-colors"
              aria-label={isMuted ? "Ativar som" : "Silenciar som"}
            >
              {isMuted ? (
                <VolumeX className="w-6 h-6" />
              ) : (
                <Volume2 className="w-6 h-6" />
              )}
            </button>

            {onVoteRequest && (
              <button
                onClick={onVoteRequest}
                className="p-3 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors"
                aria-label="Votar na performance"
              >
                <Heart className="w-6 h-6" />
              </button>
            )}
          </div>

          {/* Info */}
          <div className="flex items-center justify-center space-x-6 mt-4 text-white text-sm">
            <div className="flex items-center space-x-2">
              <Mic className="w-4 h-4" />
              <span>Cante Comigo</span>
            </div>

            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Karaokê Interativo</span>
            </div>

            {lyrics?.hasTimestamps && (
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span>Sincronizado</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </AnimatePresence>
  );
};

export default KaraokePlayer;
''''