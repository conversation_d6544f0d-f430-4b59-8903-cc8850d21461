import { Router, Request, Response } from "express";
import { param, body, query, validationResult } from "express-validator";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Playlist, PlaylistStatus } from "../models/Playlist";
import asyncHandler from "../middleware/asyncHandler";
import { createValidationError, NotFoundError } from "../utils/errors";
import { optionalAuth } from "../middleware/auth";

const router = Router();

/**
 * @swagger
 * /api/v1/restaurants/genres:
 *   get:
 *     summary: Listar gêneros disponíveis
 *     tags: [Restaurantes]
 *     responses:
 *       200:
 *         description: Lista de gêneros
 */
router.get(
  "/genres",
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    // Gêneros padrão do sistema
    const defaultGenres = {
      music: [
        {
          id: "rock",
          name: "rock",
          displayName: "Rock",
          color: "#E53E3E",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "pop",
          name: "pop",
          displayName: "Pop",
          color: "#38B2AC",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "sertanejo",
          name: "sertanejo",
          displayName: "Sertanejo",
          color: "#D69E2E",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "mpb",
          name: "mpb",
          displayName: "MPB",
          color: "#9F7AEA",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "eletronica",
          name: "eletronica",
          displayName: "Eletrônica",
          color: "#4299E1",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "funk",
          name: "funk",
          displayName: "Funk",
          color: "#F56565",
          category: "music",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
      ],
      mood: [
        {
          id: "happy",
          name: "happy",
          displayName: "Alegre",
          color: "#F6E05E",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "sad",
          name: "sad",
          displayName: "Melancolico",
          color: "#68D391",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "energetic",
          name: "energetic",
          displayName: "Energetico",
          color: "#FC8181",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "calm",
          name: "calm",
          displayName: "Calmo",
          color: "#81C784",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "romantic",
          name: "romantic",
          displayName: "Romantico",
          color: "#EF4444",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "party",
          name: "party",
          displayName: "Festa",
          color: "#8B5CF6",
          category: "mood",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
      ],
      energy: [
        {
          id: "high_energy",
          name: "high_energy",
          displayName: "Alta Energia",
          color: "#EF4444",
          category: "energy",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "medium_energy",
          name: "medium_energy",
          displayName: "Energia Média",
          color: "#F59E0B",
          category: "energy",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "low_energy",
          name: "low_energy",
          displayName: "Baixa Energia",
          color: "#3B82F6",
          category: "energy",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
      ],
      time: [
        {
          id: "morning",
          name: "morning",
          displayName: "Manhã",
          color: "#FBBF24",
          category: "time",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "afternoon",
          name: "afternoon",
          displayName: "Tarde",
          color: "#F97316",
          category: "time",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
        {
          id: "evening",
          name: "evening",
          displayName: "Noite",
          color: "#4F46E5",
          category: "time",
          isActive: true,
          isDefault: true,
          usageCount: 0,
        },
      ],
    };

    res.json({
      success: true,
      genres: defaultGenres,
      message: "Gêneros carregados com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/:restaurantId/playlist:
 *   get:
 *     summary: Obter playlist do restaurante com busca e filtros
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Termo de busca por título ou artista
 *       - in: query
 *         name: genres
 *         schema:
 *           type: string
 *         description: Lista de gêneros separados por vírgula
 *       - in: query
 *         name: moods
 *         schema:
 *           type: string
 *         description: Lista de humores separados por vírgula
 *       - in: query
 *         name: energy
 *         schema:
 *           type: string
 *         description: Lista de níveis de energia separados por vírgula
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Lista de horários separados por vírgula
 *     responses:
 *       200:
 *         description: Lista de músicas da playlist
 *       400:
 *         description: Dados inválidos
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:restaurantId/playlist",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("genres")
      .optional()
      .isString()
      .withMessage("Gêneros devem ser uma string"),
    query("moods")
      .optional()
      .isString()
      .withMessage("Humores devem ser uma string"),
    query("energy")
      .optional()
      .isString()
      .withMessage("Energia deve ser uma string"),
    query("time")
      .optional()
      .isString()
      .withMessage("Horário deve ser uma string"),
  ],
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { restaurantId } = req.params;
    const { q, genres, moods, energy, time } = req.query;

    const playlistRepository = AppDataSource.getRepository(Playlist);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId, isActive: true },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Buscar playlists ativas do restaurante
    console.log(`🔍 Buscando playlists para restaurante: ${restaurantId}`);
    const playlists = await playlistRepository.find({
      where: {
        restaurant: { id: restaurantId },
        status: PlaylistStatus.ACTIVE,
      },
      order: { createdAt: "DESC" },
    });

    console.log(`📊 Playlists encontradas: ${playlists.length}`);
    if (playlists.length > 0) {
      console.log(
        `📋 Primeira playlist: ${playlists[0].name} (${playlists[0].id})`
      );
      console.log(
        `🎵 Tracks na primeira playlist: ${
          playlists[0].tracks ? playlists[0].tracks.length : 0
        }`
      );
    }

    if (!playlists.length) {
      console.log("⚠️ Nenhuma playlist real encontrada, usando fallback mock");
      const mockTracks = [
        {
          youtubeVideoId: "dQw4w9WgXcQ",
          title: "Never Gonna Give You Up",
          artist: "Rick Astley",
          duration: 213,
          formattedDuration: "3:33",
          thumbnailUrl: "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
          addedAt: "2024-01-15T10:30:00Z",
          position: 1,
          isAvailable: true,
        },
        {
          youtubeVideoId: "9bZkp7q19f0",
          title: "PSY - GANGNAM STYLE",
          artist: "PSY",
          duration: 253,
          formattedDuration: "4:13",
          thumbnailUrl: "https://img.youtube.com/vi/9bZkp7q19f0/mqdefault.jpg",
          addedAt: "2024-01-15T11:00:00Z",
          position: 2,
          isAvailable: true,
        },
      ];

      return res.json({
        success: true,
        results: mockTracks,
        total: mockTracks.length,
        query: q || "",
        playlist: {
          id: "fallback-mock",
          name: "Playlist Fallback",
          totalTracks: mockTracks.length,
          availableTracks: mockTracks.length,
        },
        source: "mock-fallback",
      });
    }

    // Combinar todas as tracks de todas as playlists ativas
    let allTracks: any[] = [];
    let playlistInfo = {
      id: playlists[0].id,
      name: playlists[0].name,
      totalTracks: 0,
      availableTracks: 0,
    };

    for (const playlist of playlists) {
      if (playlist.tracks && Array.isArray(playlist.tracks)) {
        const playlistTracks = playlist.tracks.map((track: any) => ({
          youtubeVideoId: track.youtubeVideoId,
          title: track.title,
          artist: track.artist,
          duration: track.duration || 0,
          formattedDuration:
            track.formattedDuration || formatDuration(track.duration || 0),
          thumbnailUrl:
            track.thumbnailUrl ||
            `https://img.youtube.com/vi/${track.youtubeVideoId}/mqdefault.jpg`,
          addedAt: track.addedAt || new Date().toISOString(),
          position: track.position || 0,
          isAvailable: true,
          playlistName: playlist.name,
          playlistId: playlist.id,
          genres: track.genres || [],
          moods: track.moods || [],
          energy: track.energy || [],
          time: track.time || [],
        }));
        allTracks = [...allTracks, ...playlistTracks];
      }
    }

    // Se não há tracks reais, usar fallback
    if (!allTracks.length) {
      console.log(
        "⚠️ Playlists encontradas mas sem tracks, usando fallback mock"
      );
      const mockTracks = [
        {
          youtubeVideoId: "dQw4w9WgXcQ",
          title: "Never Gonna Give You Up",
          artist: "Rick Astley",
          duration: 213,
          formattedDuration: "3:33",
          thumbnailUrl: "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
          addedAt: "2024-01-15T10:30:00Z",
          position: 1,
          isAvailable: true,
        },
      ];

      return res.json({
        success: true,
        results: mockTracks,
        total: mockTracks.length,
        query: q || "",
        playlist: playlistInfo,
        source: "mock-fallback-no-tracks",
      });
    }

    // Aplicar filtros
    let filteredTracks = allTracks;
    if (genres && typeof genres === "string") {
      const genreList = genres.split(",").map((g) => g.trim().toLowerCase());
      filteredTracks = filteredTracks.filter((track) =>
        track.genres.some((g: string) => genreList.includes(g.toLowerCase()))
      );
    }
    if (moods && typeof moods === "string") {
      const moodList = moods.split(",").map((m) => m.trim().toLowerCase());
      filteredTracks = filteredTracks.filter((track) =>
        track.moods.some((m: string) => moodList.includes(m.toLowerCase()))
      );
    }
    if (energy && typeof energy === "string") {
      const energyList = energy.split(",").map((e) => e.trim().toLowerCase());
      filteredTracks = filteredTracks.filter((track) =>
        track.energy.some((e: string) => energyList.includes(e.toLowerCase()))
      );
    }
    if (time && typeof time === "string") {
      const timeList = time.split(",").map((t) => t.trim().toLowerCase());
      filteredTracks = filteredTracks.filter((track) =>
        track.time.some((t: string) => timeList.includes(t.toLowerCase()))
      );
    }
    if (q && typeof q === "string") {
      const query = q.toLowerCase();
      filteredTracks = filteredTracks.filter(
        (track) =>
          track.title.toLowerCase().includes(query) ||
          track.artist.toLowerCase().includes(query)
      );
    }

    playlistInfo.totalTracks = allTracks.length;
    playlistInfo.availableTracks = filteredTracks.length;

    console.log(
      `✅ Retornando ${filteredTracks.length} tracks reais de ${playlists.length} playlists`
    );

    res.json({
      success: true,
      results: filteredTracks,
      total: filteredTracks.length,
      query: q || "",
      playlist: playlistInfo,
      source: "database",
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}:
 *   get:
 *     summary: Obter dados de um restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Dados do restaurante
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:id",
  [param("id").notEmpty().withMessage("ID do restaurante é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id, isActive: true },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    res.json({
      restaurant: restaurant.toPublicJSON(),
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants:
 *   get:
 *     summary: Listar restaurantes ativos
 *     tags: [Restaurantes]
 *     responses:
 *       200:
 *         description: Lista de restaurantes
 */
router.get(
  "/",
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurants = await restaurantRepository.find({
      where: { isActive: true },
      order: { name: "ASC" },
    });

    res.json({
      restaurants: restaurants.map((r) => r.toPublicJSON()),
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/audio-settings:
 *   get:
 *     summary: Obter configurações de áudio do restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Configurações de áudio
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:id/audio-settings",
  [param("id").notEmpty().withMessage("ID do restaurante é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    const audioSettings = {
      defaultVolume: restaurant.settings?.playlist?.defaultVolume || 70,
      crossfadeDuration: restaurant.settings?.playlist?.crossfadeDuration || 3,
      shuffleMode: restaurant.settings?.playlist?.shuffleMode || false,
      repeatMode: restaurant.settings?.playlist?.repeatMode || "none",
      maxQueueSize: restaurant.settings?.playlist?.maxQueueSize || 50,
      allowDuplicates: restaurant.settings?.playlist?.allowDuplicates || false,
    };

    res.json({
      success: true,
      audioSettings,
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/audio-settings:
 *   put:
 *     summary: Atualizar configurações de áudio do restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               defaultVolume:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               crossfadeDuration:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 10
 *               shuffleMode:
 *                 type: boolean
 *               repeatMode:
 *                 type: string
 *                 enum: [none, one, all]
 *               maxQueueSize:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 200
 *               allowDuplicates:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Configurações atualizadas
 *       404:
 *         description: Restaurante não encontrado
 */
router.put(
  "/:id/audio-settings",
  [
    param("id").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("defaultVolume")
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage("Volume deve ser entre 0 e 100"),
    body("crossfadeDuration")
      .optional()
      .isFloat({ min: 0, max: 10 })
      .withMessage("Duração do crossfade deve ser entre 0 e 10 segundos"),
    body("shuffleMode")
      .optional()
      .isBoolean()
      .withMessage("Modo shuffle deve ser boolean"),
    body("repeatMode")
      .optional()
      .isIn(["none", "one", "all"])
      .withMessage("Modo repeat deve ser: none, one ou all"),
    body("maxQueueSize")
      .optional()
      .isInt({ min: 1, max: 200 })
      .withMessage("Tamanho máximo da fila deve ser entre 1 e 200"),
    body("allowDuplicates")
      .optional()
      .isBoolean()
      .withMessage("Permitir duplicatas deve ser boolean"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const {
      defaultVolume,
      crossfadeDuration,
      shuffleMode,
      repeatMode,
      maxQueueSize,
      allowDuplicates,
    } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    if (!restaurant.settings) {
      restaurant.settings = {};
    }
    if (!restaurant.settings.playlist) {
      restaurant.settings.playlist = {};
    }

    if (defaultVolume !== undefined)
      restaurant.settings.playlist.defaultVolume = defaultVolume;
    if (crossfadeDuration !== undefined)
      restaurant.settings.playlist.crossfadeDuration = crossfadeDuration;
    if (shuffleMode !== undefined)
      restaurant.settings.playlist.shuffleMode = shuffleMode;
    if (repeatMode !== undefined)
      restaurant.settings.playlist.repeatMode = repeatMode;
    if (maxQueueSize !== undefined)
      restaurant.settings.playlist.maxQueueSize = maxQueueSize;
    if (allowDuplicates !== undefined)
      restaurant.settings.playlist.allowDuplicates = allowDuplicates;

    await restaurantRepository.save(restaurant);

    res.json({
      success: true,
      message: "Configurações de áudio atualizadas com sucesso",
      audioSettings: {
        defaultVolume: restaurant.settings.playlist.defaultVolume,
        crossfadeDuration: restaurant.settings.playlist.crossfadeDuration,
        shuffleMode: restaurant.settings.playlist.shuffleMode,
        repeatMode: restaurant.settings.playlist.repeatMode,
        maxQueueSize: restaurant.settings.playlist.maxQueueSize,
        allowDuplicates: restaurant.settings.playlist.allowDuplicates,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/profile:
 *   get:
 *     summary: Obter perfil completo do restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Perfil do restaurante
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:id/profile",
  [param("id").notEmpty().withMessage("ID do restaurante é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    const profile = {
      id: restaurant.id,
      name: restaurant.name,
      email: restaurant.email || "",
      phone: restaurant.phone || "",
      address: restaurant.address || "",
      description: restaurant.description || "",
      businessHours: restaurant.settings?.schedule?.operatingHours || {
        monday: { open: "09:00", close: "22:00", isOpen: true },
        tuesday: { open: "09:00", close: "22:00", isOpen: true },
        wednesday: { open: "09:00", close: "22:00", isOpen: true },
        thursday: { open: "09:00", close: "22:00", isOpen: true },
        friday: { open: "09:00", close: "23:00", isOpen: true },
        saturday: { open: "10:00", close: "23:00", isOpen: true },
        sunday: { open: "10:00", close: "21:00", isOpen: true },
      },
      settings: {
        allowSuggestions:
          restaurant.settings?.interface?.allowAnonymousSuggestions ?? true,
        moderationRequired:
          restaurant.settings?.moderation?.requireApproval ?? true,
        maxSuggestionsPerUser:
          restaurant.settings?.moderation?.maxSuggestionsPerUser ?? 3,
        autoPlayEnabled: true,
        playlist: restaurant.settings?.playlist || {},
      },
      createdAt: restaurant.createdAt,
      updatedAt: restaurant.updatedAt,
    };

    res.json({
      success: true,
      profile,
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/profile:
 *   put:
 *     summary: Atualizar perfil do restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               description:
 *                 type: string
 *               businessHours:
 *                 type: object
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Perfil atualizado
 *       404:
 *         description: Restaurante não encontrado
 */
router.put(
  "/:id/profile",
  [
    param("id").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("name").optional().isString().withMessage("Nome deve ser uma string"),
    body("email").optional().isEmail().withMessage("Email deve ser válido"),
    body("phone")
      .optional()
      .isString()
      .withMessage("Telefone deve ser uma string"),
    body("address")
      .optional()
      .isString()
      .withMessage("Endereço deve ser uma string"),
    body("description")
      .optional()
      .isString()
      .withMessage("Descrição deve ser uma string"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const {
      name,
      email,
      phone,
      address,
      description,
      businessHours,
      settings,
    } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    if (name !== undefined) restaurant.name = name;
    if (email !== undefined) restaurant.email = email;
    if (phone !== undefined) restaurant.phone = phone;
    if (address !== undefined) restaurant.address = address;
    if (description !== undefined) restaurant.description = description;
    if (businessHours !== undefined) {
      restaurant.settings = {
        ...restaurant.settings,
        schedule: {
          ...restaurant.settings?.schedule,
          operatingHours: businessHours,
        },
      };
    }
    if (settings !== undefined) {
      restaurant.settings = {
        ...restaurant.settings,
        ...settings,
      };
    }

    await restaurantRepository.save(restaurant);

    res.json({
      success: true,
      message: "Perfil atualizado com sucesso",
      profile: {
        id: restaurant.id,
        name: restaurant.name,
        email: restaurant.email,
        phone: restaurant.phone,
        address: restaurant.address,
        description: restaurant.description,
        businessHours: restaurant.settings?.schedule?.operatingHours,
        settings: restaurant.settings,
        updatedAt: restaurant.updatedAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/status:
 *   get:
 *     summary: Verificar status de funcionamento do restaurante
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status do restaurante
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:id/status",
  [param("id").notEmpty().withMessage("ID do restaurante é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    const isOpen = restaurant.isOpen();
    const now = new Date();
    const dayOfWeek = now
      .toLocaleDateString("en-US", { weekday: "long" })
      .toLowerCase();

    const todaySchedule =
      restaurant.settings?.schedule?.operatingHours?.[dayOfWeek];

    let nextChange = null;
    if (todaySchedule && !todaySchedule.closed) {
      const currentTime = now.toTimeString().slice(0, 5);
      if (isOpen && todaySchedule.close) {
        nextChange = {
          action: "close",
          time: todaySchedule.close,
          message: `Fecha às ${todaySchedule.close}`,
        };
      } else if (!isOpen && todaySchedule.open) {
        nextChange = {
          action: "open",
          time: todaySchedule.open,
          message: `Abre às ${todaySchedule.open}`,
        };
      }
    }

    res.json({
      success: true,
      status: {
        isOpen,
        message: isOpen ? "Aberto" : "Fechado",
        nextChange,
        currentHours: todaySchedule,
        timezone:
          restaurant.settings?.schedule?.timezone || "America/Sao_Paulo",
        lastUpdated: new Date().toISOString(),
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/hours:
 *   get:
 *     summary: Obter horários de funcionamento completos
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Horários de funcionamento
 *       404:
 *         description: Restaurante não encontrado
 */
router.get(
  "/:id/hours",
  [param("id").notEmpty().withMessage("ID do restaurante é obrigatório")],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    const businessHours = restaurant.settings?.schedule?.operatingHours || {
      monday: { open: "11:00", close: "23:00", isOpen: true },
      tuesday: { open: "11:00", close: "23:00", isOpen: true },
      wednesday: { open: "11:00", close: "23:00", isOpen: true },
      thursday: { open: "11:00", close: "23:00", isOpen: true },
      friday: { open: "11:00", close: "24:00", isOpen: true },
      saturday: { open: "11:00", close: "24:00", isOpen: true },
      sunday: { open: "11:00", close: "22:00", isOpen: true },
    };

    const daysWithStatus = Object.entries(businessHours).map(
      ([day, hours]) => ({
        day,
        ...(hours || {}),
        displayName:
          {
            monday: "Segunda-feira",
            tuesday: "Terça-feira",
            wednesday: "Quarta-feira",
            thursday: "Quinta-feira",
            friday: "Sexta-feira",
            saturday: "Sábado",
            sunday: "Domingo",
          }[day] || day,
      })
    );

    res.json({
      success: true,
      hours: {
        regular: daysWithStatus,
        timezone:
          restaurant.settings?.schedule?.timezone || "America/Sao_Paulo",
        specialHours: restaurant.settings?.schedule?.playlistSchedule || [],
        lastUpdated: restaurant.updatedAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/restaurants/{id}/hours:
 *   put:
 *     summary: Atualizar horários de funcionamento
 *     tags: [Restaurantes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               businessHours:
 *                 type: object
 *               timezone:
 *                 type: string
 *               specialHours:
 *                 type: array
 *     responses:
 *       200:
 *         description: Horários atualizados
 *       404:
 *         description: Restaurante não encontrado
 */
router.put(
  "/:id/hours",
  [
    param("id").notEmpty().withMessage("ID do restaurante é obrigatório"),
    body("businessHours")
      .optional()
      .isObject()
      .withMessage("Horários devem ser um objeto"),
    body("timezone")
      .optional()
      .isString()
      .withMessage("Timezone deve ser uma string"),
    body("specialHours")
      .optional()
      .isArray()
      .withMessage("Horários especiais devem ser um array"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { id } = req.params;
    const { businessHours, timezone, specialHours } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    if (businessHours !== undefined) {
      restaurant.settings = {
        ...restaurant.settings,
        schedule: {
          ...restaurant.settings?.schedule,
          operatingHours: businessHours,
        },
      };
    }

    if (timezone !== undefined || specialHours !== undefined) {
      restaurant.settings = {
        ...restaurant.settings,
        schedule: {
          ...restaurant.settings?.schedule,
          ...(timezone && { timezone }),
          operatingHours:
            businessHours || restaurant.settings?.schedule?.operatingHours,
          ...(specialHours && { playlistSchedule: specialHours }),
        },
      };
    }

    await restaurantRepository.save(restaurant);

    res.json({
      success: true,
      message: "Horários atualizados com sucesso",
      hours: {
        regular: restaurant.settings?.schedule?.operatingHours,
        timezone: restaurant.settings?.schedule?.timezone,
        specialHours: restaurant.settings?.schedule?.playlistSchedule,
        lastUpdated: restaurant.updatedAt,
      },
    });
  })
);

// Função auxiliar para formatar duração
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

export default router;
