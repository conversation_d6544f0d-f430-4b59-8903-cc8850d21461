import React, { useState, useEffect, useCallback } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  ThumbsUp,
  ThumbsDown,
  Search,
  Play,
  Users,
  Heart,
  Star,
  TrendingUp,
  Headphones,
  RefreshCw,
  User,
  CreditCard,
  Mic,
  Trophy,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "../../config/api";
import sessionService, { ClientSession, UserStats } from "@/services/sessionService";
import { useWebSocket } from "@/services/websocket";
import MusicFilters from "@/components/music/MusicFilters";
import PaymentModal from "@/components/client/PaymentModal";
import KaraokePlayer from "@/components/client/KaraokePlayer";
import PlaybackQueue from "@/components/client/PlaybackQueue";
import RewardsPanel from "@/components/client/RewardsPanel";
import { NewSuggestionAlert } from "@/components/client/NewSuggestionAlert";
import { TableLeaderboard } from "@/components/client/TableLeaderboard";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName: string;
  viewCount: number;
  publishedAt: string;
  youtubeVideoId: string;
}

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  status: "pending" | "approved" | "rejected";
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  isPaid?: boolean;
  clientSessionId?: string;
  duration?: number;
  thumbnailUrl?: string;
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isOpen: boolean;
}

const ClientInterface: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [searchParams] = useSearchParams();
  const tableNumber = searchParams.get("table");

  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Suggestion | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [userVotes, setUserVotes] = useState<{ [key: string]: "up" | "down" }>({});
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [playlistPage, setPlaylistPage] = useState(1);
  const [playlistLoading, setPlaylistLoading] = useState(false);
  const [hasMorePlaylist, setHasMorePlaylist] = useState(true);
  const [totalPlaylistSongs, setTotalPlaylistSongs] = useState(0);
  const [session, setSession] = useState<ClientSession | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    points: 0,
    level: 1,
    badges: [],
    suggestionsCount: 0,
    votesCount: 0,
    streak: 0,
  });
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [showBadgeEarned, setShowBadgeEarned] = useState<string | null>(null);
  const [showNameInput, setShowNameInput] = useState(false);
  const [clientName, setClientName] = useState("");
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedSongForPayment, setSelectedSongForPayment] = useState<Song | null>(null);
  const [showKaraoke, setShowKaraoke] = useState(false);
  const [karaokeData, setKaraokeData] = useState<Suggestion | null>(null);
  const [showQueue, setShowQueue] = useState(true);
  const [queueCollapsed, setQueueCollapsed] = useState(false);
  const [showRewards, setShowRewards] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);

  const { isConnected, joinRestaurant, on, off } = useWebSocket();

  const initializeSession = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const newSession = await sessionService.forceNewSession(restaurantId, tableNumber || undefined, clientName || undefined);
      setSession(newSession);
      setUserStats({
        points: newSession.points || 0,
        level: newSession.level || 1,
        badges: newSession.badges || [],
        suggestionsCount: newSession.suggestionsCount || 0,
        votesCount: newSession.votesCount || 0,
        streak: newSession.streak || 0,
      });
      if (!newSession.clientName && !clientName) setShowNameInput(true);
    } catch (error) {
      console.error("Error initializing session:", error);
      toast.error("Erro ao inicializar sessão");
    }
  }, [restaurantId, tableNumber, clientName]);

  const setupWebSocketListeners = useCallback(() => {
    on("new-suggestion", (data: Suggestion) => {
      loadSuggestions();
      toast.success(`Nova música: ${data.title}`, { duration: 3000 });
    });
    on("vote-update", () => loadSuggestions());
    on("queue-update", () => loadSuggestions());
    on("suggestion-approved", (data: Suggestion) => {
      loadSuggestions();
      toast.success(`✅ Música aprovada: ${data.title}`, { duration: 4000 });
    });
    on("now-playing", (data: { suggestion: Suggestion }) => {
      setCurrentlyPlaying(data.suggestion);
      toast(`🎵 Tocando: ${data.suggestion.title}`, { duration: 5000 });
    });
  }, []);

  const cleanupWebSocketListeners = useCallback(() => {
    off("new-suggestion");
    off("vote-update");
    off("queue-update");
    off("suggestion-approved");
    off("now-playing");
  }, [off]);

  const loadRestaurantInfo = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const response = await fetch(buildApiUrl(`/restaurants/${restaurantId}`));
      if (response.ok) {
        const data = await response.json();
        setRestaurant(data.restaurant);
      } else {
        setRestaurant({
          id: restaurantId,
          name: "Restaurante Demo",
          description: "Ambiente acolhedor com música interativa",
          isActive: true,
          isOpen: true,
        });
      }
    } catch (error) {
      console.error("Erro ao carregar restaurante:", error);
      toast.error("Erro ao carregar informações do restaurante");
    }
  }, [restaurantId]);

  const loadSuggestions = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const params = new URLSearchParams({ status: "approved" });
      if (activeFilters.length) {
        const genreFilters = activeFilters.filter((f) => ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(f));
        if (genreFilters.length) params.append("genres", genreFilters.join(","));
        const moodFilters = activeFilters.filter((f) => ["happy", "sad", "energetic", "calm"].includes(f));
        if (moodFilters.length) params.append("moods", moodFilters.join(","));
      }
      const response = await fetch(buildApiUrl(`/suggestions/${restaurantId}?${params.toString()}`));
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
      } else {
        setSuggestions([
          {
            id: "1",
            title: "Bohemian Rhapsody",
            artist: "Queen",
            youtubeVideoId: "fJ9rUzIMcZQ",
            status: "approved",
            upvotes: 15,
            downvotes: 2,
            score: 13,
            createdAt: new Date().toISOString(),
            thumbnailUrl: "https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",
          },
          {
            id: "2",
            title: "Hotel California",
            artist: "Eagles",
            youtubeVideoId: "BciS5krYL80",
            status: "approved",
            upvotes: 12,
            downvotes: 1,
            score: 11,
            createdAt: new Date().toISOString(),
            thumbnailUrl: "https://img.youtube.com/vi/BciS5krYL80/mqdefault.jpg",
          },
        ]);
      }
    } catch (error) {
      console.error("Erro ao carregar sugestões:", error);
      toast.error("Erro ao carregar fila de músicas");
    }
  }, [restaurantId, activeFilters]);

  const loadCurrentlyPlaying = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const response = await fetch(buildApiUrl(`/playback/${restaurantId}/state`));
      if (response.ok) {
        const data = await response.json();
        setCurrentlyPlaying(data.state?.currentTrack || null);
      }
    } catch (error) {
      console.error("Erro ao carregar música atual:", error);
      toast.error("Erro ao carregar música atual");
    }
  }, [restaurantId]);

  const loadAvailableMusic = useCallback(async (page: number = 1, loadMore: boolean = false) => {
    if (!restaurantId) return;
    try {
      setPlaylistLoading(true);
      const limit = 24;
      const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
      if (activeFilters.length) {
        const genreFilters = activeFilters.filter((f) => ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(f));
        if (genreFilters.length) params.append("genres", genreFilters.join(","));
        const moodFilters = activeFilters.filter((f) => ["happy", "sad", "energetic", "calm"].includes(f));
        if (moodFilters.length) params.append("moods", moodFilters.join(","));
      }
      const response = await fetch(buildApiUrl(`/restaurants/${restaurantId}/playlist?${params.toString()}`));
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.results) {
          const transformedResults = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration || 0,
            formattedDuration: track.formattedDuration || "0:00",
            thumbnailUrl: track.thumbnailUrl || `https://img.youtube.com/vi/${track.youtubeVideoId || track.id}/mqdefault.jpg`,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: track.viewCount || 0,
            publishedAt: track.addedAt || new Date().toISOString(),
          }));
          setSearchResults((prev) => (loadMore ? [...prev, ...transformedResults] : transformedResults));
          setTotalPlaylistSongs(data.total || transformedResults.length);
          setHasMorePlaylist(transformedResults.length === limit);
          setPlaylistPage(page);
        } else {
          setSearchResults([]);
          toast.info("Nenhuma música encontrada");
        }
      } else {
        toast.error("Erro ao carregar playlist");
      }
    } catch (error) {
      console.error("Erro ao carregar playlist:", error);
      toast.error("Erro ao carregar playlist");
    } finally {
      setPlaylistLoading(false);
    }
  }, [restaurantId, activeFilters]);

  const searchSongs = useCallback(async () => {
    if (!searchQuery.trim() || !restaurantId) return;
    try {
      setSearchLoading(true);
      const params = new URLSearchParams({ q: encodeURIComponent(searchQuery) });
      if (activeFilters.length) {
        const genreFilters = activeFilters.filter((f) => ["rock", "pop", "sertanejo", "mpb", "eletronica", "funk"].includes(f));
        if (genreFilters.length) params.append("genres", genreFilters.join(","));
        const moodFilters = activeFilters.filter((f) => ["happy", "sad", "energetic", "calm"].includes(f));
        if (moodFilters.length) params.append("moods", moodFilters.join(","));
      }
      const response = await fetch(buildApiUrl(`/restaurants/${restaurantId}/playlist?${params.toString()}`));
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.results) {
          const transformedResults = data.results.map((track: any) => ({
            id: track.youtubeVideoId || track.id,
            title: track.title,
            artist: track.artist,
            duration: track.duration || 0,
            formattedDuration: track.formattedDuration || "0:00",
            thumbnailUrl: track.thumbnailUrl || `https://img.youtube.com/vi/${track.youtubeVideoId || track.id}/mqdefault.jpg`,
            youtubeVideoId: track.youtubeVideoId || track.id,
            channelName: track.artist,
            viewCount: track.viewCount || 0,
            publishedAt: track.addedAt || new Date().toISOString(),
          }));
          setSearchResults(transformedResults);
          setTotalPlaylistSongs(data.total || transformedResults.length);
          setHasMorePlaylist(false);
          setPlaylistPage(1);
          toast.success(`${transformedResults.length} música(s) encontrada(s)`);
        } else {
          setSearchResults([]);
          toast.info(`Nenhuma música encontrada para "${searchQuery}"`);
        }
      } else {
        toast.error("Erro ao buscar músicas");
      }
    } catch (error) {
      console.error("Erro ao buscar músicas:", error);
      toast.error("Erro ao buscar músicas");
    } finally {
      setSearchLoading(false);
    }
  }, [searchQuery, restaurantId, activeFilters]);

  const suggestSong = useCallback(async (song: Song) => {
    if (!session || !restaurantId) {
      toast.error("Sessão ou restaurante não encontrado");
      return;
    }
    try {
      setLoading(true);
      const response = await fetch(buildApiUrl("/queues/add"), {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-Session-ID": session.sessionToken },
        body: JSON.stringify({
          restaurantId,
          song: { id: song.id, videoId: song.youtubeVideoId, title: song.title, artist: song.artist },
          isPriority: false,
        }),
      });
      if (response.ok) {
        toast.success("Música adicionada à fila normal! 🎵");
        loadSuggestions();
        awardPoints("suggest", 10);
        setSearchQuery("");
        if (searchQuery.trim()) loadAvailableMusic(1);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Erro ao adicionar música");
      }
    } catch (error) {
      console.error("Erro ao sugerir música:", error);
      toast.error("Erro ao adicionar música");
    } finally {
      setLoading(false);
    }
  }, [session, restaurantId, loadSuggestions, searchQuery]);

  const suggestSongWithPayment = useCallback((song: Song) => {
    setSelectedSongForPayment(song);
    setShowPaymentModal(true);
  }, []);

  const handlePaymentSuccess = useCallback(async (paymentId: string) => {
    if (!selectedSongForPayment || !session || !restaurantId) return;
    try {
      const response = await fetch(buildApiUrl("/queues/add"), {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-Session-ID": session.sessionToken },
        body: JSON.stringify({
          restaurantId,
          song: {
            id: selectedSongForPayment.id,
            videoId: selectedSongForPayment.youtubeVideoId,
            title: selectedSongForPayment.title,
            artist: selectedSongForPayment.artist,
          },
          isPriority: true,
        }),
      });
      if (response.ok) {
        toast.success("Música adicionada à fila prioritária! 🎵");
        loadSuggestions();
        awardPoints("suggest", 25);
        setSearchQuery("");
        if (searchQuery.trim()) loadAvailableMusic(1);
        setKaraokeData({
          id: selectedSongForPayment.id,
          title: selectedSongForPayment.title,
          artist: selectedSongForPayment.artist,
          thumbnailUrl: selectedSongForPayment.thumbnailUrl,
          duration: selectedSongForPayment.duration,
          youtubeVideoId: selectedSongForPayment.youtubeVideoId,
        });
        setShowPaymentModal(false);
        setTimeout(() => {
          toast(
            (t) => (
              <div className="flex flex-col gap-2">
                <span>🎤 Quer cantar junto? Ative o "Cante Comigo"!</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setShowKaraoke(true);
                      toast.dismiss(t.id);
                    }}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm"
                  >
                    Sim, vamos cantar!
                  </button>
                  <button
                    onClick={() => toast.dismiss(t.id)}
                    className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                  >
                    Não, obrigado
                  </button>
                </div>
              </div>
            ),
            { duration: 8000 }
          );
        }, 2000);
      } else {
        toast.error("Erro ao processar pagamento");
      }
    } catch (error) {
      console.error("Erro após pagamento:", error);
      toast.error("Erro ao processar pagamento");
    } finally {
      setSelectedSongForPayment(null);
    }
  }, [selectedSongForPayment, session, restaurantId, loadSuggestions, searchQuery]);

  const voteSuggestion = useCallback(async (suggestionId: string, voteType: "up" | "down") => {
    if (!session || !restaurantId) {
      toast.error("Sessão ou restaurante não encontrado");
      return;
    }
    try {
      const response = await fetch(buildApiUrl(`/suggestions/${suggestionId}/vote`), {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-Session-ID": session.sessionToken },
        body: JSON.stringify({ voteType }),
      });
      if (response.ok) {
        setUserVotes((prev) => ({ ...prev, [suggestionId]: voteType }));
        sessionService.incrementVotes();
        const updatedSession = sessionService.getSession();
        if (updatedSession) {
          setUserStats({
            points: updatedSession.points,
            level: updatedSession.level,
            badges: updatedSession.badges,
            suggestionsCount: updatedSession.suggestionsCount,
            votesCount: updatedSession.votesCount,
            streak: updatedSession.streak,
          });
        }
        loadSuggestions();
        toast.success(`Voto ${voteType === "up" ? "positivo" : "negativo"} registrado! +10 pontos!`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Erro ao votar");
      }
    } catch (error) {
      console.error("Erro ao votar:", error);
      toast.error("Erro ao votar");
    }
  }, [session, restaurantId, loadSuggestions]);

  const saveClientName = useCallback(async () => {
    if (!clientName.trim()) {
      toast.error("Por favor, digite seu nome");
      return;
    }
    if (!restaurantId) return;
    try {
      const updatedSession = await sessionService.createSession(restaurantId, tableNumber || undefined, clientName);
      setSession(updatedSession);
      setShowNameInput(false);
      toast.success(`Bem-vindo, ${clientName}! 🎵`);
    } catch (error) {
      console.error("Erro ao salvar nome:", error);
      toast.error("Erro ao salvar nome");
    }
  }, [clientName, restaurantId, tableNumber]);

  const awardPoints = useCallback((action: "suggest" | "vote", amount: number = 10) => {
    setUserStats((prev) => {
      const newStats = {
        ...prev,
        points: prev.points + amount,
        suggestionsCount: action === "suggest" ? prev.suggestionsCount + 1 : prev.suggestionsCount,
        votesCount: action === "vote" ? prev.votesCount + 1 : prev.votesCount,
        streak: prev.streak + 1,
      };
      const newLevel = Math.floor(newStats.points / 100) + 1;
      const newBadges = getBadges(newStats);
      if (newLevel > prev.level) {
        setShowLevelUp(true);
        setTimeout(() => setShowLevelUp(false), 3000);
        toast.success(`🎉 Level Up! Agora você é nível ${newLevel}!`);
      }
      const earnedBadge = newBadges.find((badge) => !prev.badges.includes(badge));
      if (earnedBadge) {
        sessionService.awardBadge(earnedBadge);
        setShowBadgeEarned(earnedBadge);
        setTimeout(() => setShowBadgeEarned(null), 3000);
        toast.success(`🏆 Nova conquista: ${earnedBadge}!`);
      }
      return { ...newStats, level: newLevel, badges: newBadges };
    });
  }, []);

  const getBadges = useCallback((stats: UserStats) => {
    const badges = [];
    if (stats.suggestionsCount >= 1) badges.push("🎵 Primeira Sugestão");
    if (stats.suggestionsCount >= 5) badges.push("🎶 Melomaníaco");
    if (stats.suggestionsCount >= 10) badges.push("🎸 DJ Amador");
    if (stats.votesCount >= 10) badges.push("👍 Crítico Musical");
    if (stats.votesCount >= 25) badges.push("⭐ Especialista");
    if (stats.streak >= 3) badges.push("🔥 Em Chamas");
    if (stats.points >= 500) badges.push("🏆 Lenda");
    return badges;
  }, []);

  useEffect(() => {
    if (!restaurantId) return;
    initializeSession();
    loadRestaurantInfo();
    loadSuggestions();
    loadCurrentlyPlaying();
    loadAvailableMusic();
    joinRestaurant(restaurantId);
    setupWebSocketListeners();
    return () => cleanupWebSocketListeners();
  }, [restaurantId, initializeSession, loadRestaurantInfo, loadSuggestions, loadCurrentlyPlaying, loadAvailableMusic, setupWebSocketListeners, cleanupWebSocketListeners]);

  useEffect(() => {
    if (restaurantId) {
      loadSuggestions();
      setPlaylistPage(1);
      loadAvailableMusic(1);
    }
  }, [activeFilters, restaurantId, loadSuggestions, loadAvailableMusic]);

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center text-white">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4" aria-hidden="true" />
          <h2 className="text-xl font-semibold">Carregando...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <header className="bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
              <Music className="w-6 h-6" aria-hidden="true" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">{restaurant.name}</h1>
              {tableNumber && <p className="text-sm text-purple-200">Mesa {tableNumber}</p>}
            </div>
          </div>
          <p className="text-sm text-purple-200 mb-4">Escolha e vote nas músicas que vão animar seu momento!</p>
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-400" aria-hidden="true" />
              <span>Nível {userStats.level}</span>
            </div>
            <div className="flex items-center gap-1">
              <TrendingUp className="w-4 h-4 text-green-400" aria-hidden="true" />
              <span>{userStats.points} pts</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="w-4 h-4 text-red-400" aria-hidden="true" />
              <span>{userStats.suggestionsCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <ThumbsUp className="w-4 h-4 text-blue-400" aria-hidden="true" />
              <span>{userStats.votesCount}</span>
            </div>
            <button
              onClick={() => setShowRewards(true)}
              className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full hover:from-yellow-600 hover:to-orange-600 transition-transform hover:scale-105"
              aria-label="Ver prêmios"
            >
              <Trophy className="w-4 h-4" aria-hidden="true" />
              <span>Prêmios</span>
            </button>
            <button
              onClick={() => setShowLeaderboard(true)}
              className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105"
              aria-label="Ver ranking"
            >
              <Users className="w-4 h-4" aria-hidden="true" />
              <span>Ranking</span>
            </button>
          </div>
          {userStats.badges.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mt-3">
              {userStats.badges.slice(0, 3).map((badge, index) => (
                <span key={index} className="px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20">
                  {badge}
                </span>
              ))}
              {userStats.badges.length > 3 && (
                <span className="px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20">
                  +{userStats.badges.length - 3} mais
                </span>
              )}
            </div>
          )}
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-6 space-y-6">
        {currentlyPlaying && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
            aria-labelledby="now-playing"
          >
            <h2 id="now-playing" className="flex items-center gap-2 text-xl font-bold mb-4">
              <Play className="w-5 h-5 text-green-400" aria-hidden="true" />
              Tocando Agora
            </h2>
            <div className="flex items-center gap-4">
              <img
                src={currentlyPlaying.thumbnailUrl || `https://img.youtube.com/vi/${currentlyPlaying.youtubeVideoId}/mqdefault.jpg`}
                alt={`Capa de ${currentlyPlaying.title}`}
                className="w-16 h-16 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h3 className="font-semibold truncate">{currentlyPlaying.title}</h3>
                <p className="text-purple-200 truncate">{currentlyPlaying.artist}</p>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-green-400">
                  {currentlyPlaying.score > 0 ? "+" : ""}{currentlyPlaying.score}
                </div>
                <div className="text-xs text-purple-200">votos</div>
              </div>
            </div>
          </motion.section>
        )}

        <section className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20" aria-labelledby="search">
          <h2 id="search" className="flex items-center gap-2 text-xl font-bold mb-4">
            <Search className="w-5 h-5" aria-hidden="true" />
            Buscar Músicas
          </h2>
          <div className="flex gap-3 mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && searchSongs()}
              placeholder="Busque por música ou artista..."
              className="flex-1 px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
              aria-label="Buscar músicas por título ou artista"
            />
            <button
              onClick={searchSongs}
              disabled={searchLoading || !searchQuery.trim()}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2"
              aria-label="Buscar músicas"
            >
              {searchLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" aria-hidden="true" />
              ) : (
                <Search className="w-4 h-4" aria-hidden="true" />
              )}
              <span>Buscar</span>
            </button>
          </div>
        </section>

        <section className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20" aria-labelledby="filters">
          <h2 id="filters" className="flex items-center gap-2 text-xl font-bold mb-4">
            <Music className="w-5 h-5" aria-hidden="true" />
            Filtros de Música
          </h2>
          <MusicFilters
            activeFilters={activeFilters}
            onFiltersChange={setActiveFilters}
          />
        </section>

        <section className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20" aria-labelledby="playlist">
          <div className="flex items-center justify-between mb-4">
            <h2 id="playlist" className="flex items-center gap-2 text-xl font-bold">
              <Music className="w-5 h-5" aria-hidden="true" />
              Playlist do Restaurante
            </h2>
            {totalPlaylistSongs > 0 && (
              <div className="text-sm text-purple-200">
                {searchResults.length} de {totalPlaylistSongs} músicas
              </div>
            )}
          </div>
          {searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Headphones className="w-12 h-12 text-purple-400 mx-auto mb-3" aria-hidden="true" />
              <p className="text-purple-200">Nenhuma música na playlist ainda.</p>
              <p className="text-sm text-purple-300">Use a busca ou filtros para encontrar músicas.</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {searchResults.map((song) => (
                  <motion.div
                    key={song.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.03 }}
                    className="bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all"
                    role="article"
                    aria-labelledby={`song-${song.id}`}
                  >
                    <img
                      src={song.thumbnailUrl}
                      alt={`Capa de ${song.title}`}
                      className="w-full h-32 object-cover rounded-lg mb-3"
                    />
                    <h3 id={`song-${song.id}`} className="font-semibold text-sm truncate">{song.title}</h3>
                    <p className="text-purple-200 text-xs truncate mb-2">{song.artist}</p>
                    <div className="flex justify-between text-xs text-purple-300 mb-3">
                      <span>{song.formattedDuration}</span>
                      <span>{song.genre || "N/A"}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <button
                        onClick={() => suggestSong(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs"
                        aria-label={`Adicionar ${song.title} à fila normal`}
                      >
                        <Music className="w-3 h-3" aria-hidden="true" />
                        <span>Fila Normal</span>
                      </button>
                      <button
                        onClick={() => suggestSongWithPayment(song)}
                        disabled={loading}
                        className="px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs"
                        aria-label={`Adicionar ${song.title} à fila prioritária por R$ 2`}
                      >
                        <CreditCard className="w-3 h-3" aria-hidden="true" />
                        <span>Prioridade R$ 2</span>
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
              {hasMorePlaylist && (
                <div className="text-center">
                  <button
                    onClick={() => loadAvailableMusic(playlistPage + 1, true)}
                    disabled={playlistLoading}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto"
                    aria-label="Carregar mais músicas da playlist"
                  >
                    {playlistLoading ? (
                      <RefreshCw className="w-4 h-4 animate-spin" aria-hidden="true" />
                    ) : (
                      <Music className="w-4 h-4" aria-hidden="true" />
                    )}
                    <span>{playlistLoading ? "Carregando..." : "Carregar Mais Músicas"}</span>
                  </button>
                </div>
              )}
              {!hasMorePlaylist && totalPlaylistSongs > 24 && (
                <p className="text-center text-sm text-purple-300">
                  🎵 Todas as {totalPlaylistSongs} músicas foram carregadas!
                </p>
              )}
            </>
          )}
        </section>

        {showQueue && (
          <section className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20" aria-labelledby="queue">
            <h2 id="queue" className="flex items-center gap-2 text-xl font-bold mb-4">
              <TrendingUp className="w-5 h-5" aria-hidden="true" />
              Fila de Músicas
            </h2>
            {suggestions.length === 0 ? (
              <div className="text-center py-8">
                <Headphones className="w-12 h-12 text-purple-400 mx-auto mb-3" aria-hidden="true" />
                <p className="text-purple-200">Nenhuma música na fila ainda.</p>
                <p className="text-sm text-purple-300">Seja o primeiro a sugerir!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {suggestions.map((suggestion, index) => (
                  <motion.div
                    key={suggestion.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-4 p-4 bg-white/5 rounded-lg border border-white/10"
                    role="article"
                    aria-labelledby={`suggestion-${suggestion.id}`}
                  >
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-300">#{index + 1}</div>
                    </div>
                    <img
                      src={suggestion.thumbnailUrl || `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`}
                      alt={`Capa de ${suggestion.title}`}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h3 id={`suggestion-${suggestion.id}`} className="font-semibold truncate">{suggestion.title}</h3>
                      <p className="text-purple-200 truncate">{suggestion.artist}</p>
                      <div className="flex items-center gap-4 text-sm mt-1">
                        <div className="flex items-center gap-1 text-green-400">
                          <ThumbsUp className="w-3 h-3" aria-hidden="true" />
                          <span>{suggestion.upvotes}</span>
                        </div>
                        <div className="flex items-center gap-1 text-red-400">
                          <ThumbsDown className="w-3 h-3" aria-hidden="true" />
                          <span>{suggestion.downvotes}</span>
                        </div>
                        <div className="text-purple-300">Score: {suggestion.score}</div>
                        {suggestion.isPaid && (
                          <div className="flex items-center gap-1 text-yellow-400">
                            <CreditCard className="w-3 h-3" aria-hidden="true" />
                            <span className="text-xs">PAGO</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {suggestion.isPaid && suggestion.clientSessionId === session?.id && (
                        <button
                          onClick={() => {
                            setKaraokeData({
                              id: suggestion.youtubeVideoId,
                              title: suggestion.title,
                              artist: suggestion.artist,
                              thumbnailUrl: suggestion.thumbnailUrl || `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`,
                              duration: suggestion.duration || 0,
                              youtubeVideoId: suggestion.youtubeVideoId,
                            });
                            setShowKaraoke(true);
                          }}
                          className="p-2 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                          aria-label={`Ativar karaokê para ${suggestion.title}`}
                        >
                          <Mic className="w-4 h-4" aria-hidden="true" />
                        </button>
                      )}
                      <button
                        onClick={() => voteSuggestion(suggestion.id, "up")}
                        disabled={userVotes[suggestion.id] === "up"}
                        className={`p-2 rounded-lg transition-colors ${userVotes[suggestion.id] === "up" ? "bg-green-600" : "bg-white/20 text-green-400 hover:bg-green-600 hover:text-white"}`}
                        aria-label={`Votar positivamente em ${suggestion.title}`}
                      >
                        <ThumbsUp className="w-4 h-4" aria-hidden="true" />
                      </button>
                      <button
                        onClick={() => voteSuggestion(suggestion.id, "down")}
                        disabled={userVotes[suggestion.id] === "down"}
                        className={`p-2 rounded-lg transition-colors ${userVotes[suggestion.id] === "down" ? "bg-red-600" : "bg-white/20 text-red-400 hover:bg-red-600 hover:text-white"}`}
                        aria-label={`Votar negativamente em ${suggestion.title}`}
                      >
                        <ThumbsDown className="w-4 h-4" aria-hidden="true" />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </section>
        )}

        {showQueue && (
          <section className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20" aria-labelledby="playback-queue">
            <PlaybackQueue
              restaurantId={restaurantId || ""}
              sessionId={session?.id || ""}
              isCollapsed={queueCollapsed}
              onToggleCollapse={() => setQueueCollapsed(!queueCollapsed)}
            />
          </section>
        )}

        <footer className="text-center py-6">
          <p className="text-sm text-purple-300">🎵 Powered by Sistema de Playlist Interativa</p>
        </footer>
      </main>

      <AnimatePresence>
        {showLevelUp && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🎉</div>
              <h3 className="text-xl font-bold mb-1">LEVEL UP!</h3>
              <p className="text-sm">Você alcançou o nível {userStats.level}!</p>
            </div>
          </motion.div>
        )}
        {showBadgeEarned && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.5, y: -50 }}
            className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50"
            role="alert"
            aria-live="polite"
          >
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center">
              <div className="text-4xl mb-2">🏆</div>
              <h3 className="text-xl font-bold mb-1">NOVA CONQUISTA!</h3>
              <p className="text-sm">{showBadgeEarned}</p>
            </div>
          </motion.div>
        )}
        {showNameInput && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            role="dialog"
            aria-modal="true"
            aria-labelledby="name-modal-title"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white rounded-xl p-6 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-6">
                <User className="w-12 h-12 text-purple-500 mx-auto mb-3" aria-hidden="true" />
                <h3 id="name-modal-title" className="text-xl font-bold text-gray-800 mb-2">Bem-vindo! 🎵</h3>
                <p className="text-sm text-gray-600">Como podemos te chamar? (Opcional)</p>
              </div>
              <div className="space-y-4">
                <input
                  type="text"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Digite seu nome..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                  onKeyPress={(e) => e.key === "Enter" && saveClientName()}
                  autoFocus
                  aria-label="Digite seu nome"
                />
                <div className="flex gap-3">
                  <button
                    onClick={saveClientName}
                    disabled={!clientName.trim()}
                    className="flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50"
                    aria-label="Confirmar nome"
                  >
                    Continuar
                  </button>
                  <button
                    onClick={() => setShowNameInput(false)}
                    className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg hover:bg-gray-300 transition-colors"
                    aria-label="Pular entrada de nome"
                  >
                    Pular
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
        {showPaymentModal && selectedSongForPayment && (
          <PaymentModal
            isOpen={showPaymentModal}
            onClose={() => {
              setShowPaymentModal(false);
              setSelectedSongForPayment(null);
            }}
            suggestion={{
              id: selectedSongForPayment.id,
              title: selectedSongForPayment.title,
              artist: selectedSongForPayment.artist,
              thumbnailUrl: selectedSongForPayment.thumbnailUrl,
              duration: selectedSongForPayment.duration,
              youtubeVideoId: selectedSongForPayment.youtubeVideoId,
            }}
            sessionId={session?.sessionToken || ""}
            restaurantId={restaurantId || ""}
            onPaymentSuccess={handlePaymentSuccess}
          />
        )}
        {karaokeData && (
          <KaraokePlayer
            isOpen={showKaraoke}
            onClose={() => {
              setShowKaraoke(false);
              setKaraokeData(null);
            }}
            suggestion={karaokeData}
            sessionId={session?.id || ""}
            onVoteRequest={() => toast.success("Votação solicitada! 🗳️", { duration: 4000 })}
          />
        )}
        {session && restaurantId && (
          <NewSuggestionAlert
            restaurantId={restaurantId}
            sessionId={session.id}
            onVote={(suggestionId, voteType) => {
              voteSuggestion(suggestionId, voteType);
              awardPoints("vote", 10);
            }}
          />
        )}
        {showRewards && session && (
          <RewardsPanel
            sessionId={session.id}
            restaurantId={restaurantId || ""}
            isOpen={showRewards}
            onClose={() => setShowRewards(false)}
          />
        )}
        <TableLeaderboard
          restaurantId={restaurantId || ""}
          currentTableNumber={tableNumber}
          isVisible={showLeaderboard}
          onClose={() => setShowLeaderboard(false)}
        />
      </AnimatePresence>
    </div>
  );
};

export default ClientInterface;
