import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Shield,
  Ban,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  Clock,
  Music,
  User,
  Settings,
  Plus,
  Trash2,
  Edit,
  Save,
  RefreshCw,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { useRestaurantContext } from "../restaurant/RestaurantDashboard";
import { buildApiUrl } from "../../config/api";
import { apiService } from "../../services/api";

interface ModerationRule {
  id: string;
  name: string;
  type: "blacklist" | "whitelist" | "duration" | "explicit" | "genre";
  value: string;
  isActive: boolean;
  createdAt: string;
}

interface PendingSuggestion {
  id: string;
  title: string;
  artist: string;
  duration: string;
  thumbnailUrl: string;
  submittedBy: string;
  submittedAt: string;
  votes: number;
  flags: string[];
  riskLevel: "low" | "medium" | "high";
  autoModerationResult?: {
    action: "approve" | "reject" | "review";
    reason: string;
    confidence: number;
  };
}

const AdvancedModeration: React.FC = () => {
  const { restaurantId } = useRestaurantContext();
  const [pendingSuggestions, setPendingSuggestions] = useState<
    PendingSuggestion[]
  >([]);
  const [moderationRules, setModerationRules] = useState<ModerationRule[]>([]);
  const [loading, setLoading] = useState(false);

  // Debug: Log do restaurantId
  useEffect(() => {
    console.log("🔍 AdvancedModeration - restaurantId:", restaurantId);
  }, [restaurantId]);
  const [activeTab, setActiveTab] = useState<"pending" | "rules" | "settings">(
    "pending"
  );
  const [newRule, setNewRule] = useState({
    name: "",
    type: "blacklist" as ModerationRule["type"],
    value: "",
  });
  const [showAddRule, setShowAddRule] = useState(false);

  // Configurações de moderação
  const [moderationSettings, setModerationSettings] = useState({
    autoApprove: false,
    autoReject: true,
    requireManualReview: true,
    maxDuration: 600, // 10 minutos
    minVotesForAutoApproval: 10,
    maxVotesForAutoRejection: -5,
    explicitContentFilter: true,
    workingHours: {
      enabled: true,
      start: "11:00",
      end: "23:00",
    },
  });

  useEffect(() => {
    if (restaurantId) {
      loadPendingSuggestions();
      loadModerationRules();
    }
  }, [restaurantId]);

  const loadPendingSuggestions = async () => {
    if (!restaurantId) return;

    setLoading(true);
    try {
      console.log(
        "🔄 Carregando sugestões pendentes para moderação:",
        restaurantId
      );

      // Buscar sugestões pendentes de moderação
      const url = buildApiUrl(`/suggestions/${restaurantId}`, {
        status: "pending",
        limit: "50",
        page: "1",
      });

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Erro ao carregar sugestões: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("📡 Sugestões pendentes carregadas:", data);

      // Mapear sugestões para o formato esperado
      const suggestions: PendingSuggestion[] =
        data.suggestions?.map((suggestion: any) => ({
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist,
          duration: formatDuration(suggestion.duration),
          thumbnailUrl:
            suggestion.thumbnailUrl ||
            `https://img.youtube.com/vi/${suggestion.youtubeVideoId}/mqdefault.jpg`,
          submittedBy:
            suggestion.client_name ||
            `Mesa ${suggestion.table_number}` ||
            "Cliente",
          submittedAt: suggestion.createdAt,
          votes: suggestion.voteCount || 0,
          flags: suggestion.moderationFlags || [],
          riskLevel: calculateRiskLevel(suggestion),
          autoModerationResult: suggestion.moderationResult || null,
        })) || [];

      setPendingSuggestions(suggestions);
    } catch (error) {
      console.error("❌ Erro ao carregar sugestões pendentes:", error);
      toast.error("Erro ao carregar sugestões pendentes");
      // Fallback para dados vazios em caso de erro
      setPendingSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  // Função auxiliar para calcular nível de risco
  const calculateRiskLevel = (suggestion: any): "low" | "medium" | "high" => {
    const flags = suggestion.moderationFlags || [];
    const voteCount = suggestion.voteCount || 0;
    const duration = suggestion.duration || 0;

    if (
      flags.includes("explicit") ||
      flags.includes("inappropriate") ||
      voteCount < -3
    ) {
      return "high";
    }

    if (duration > 600 || flags.length > 0 || voteCount < 0) {
      return "medium";
    }

    return "low";
  };

  // Função auxiliar para formatar duração
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const loadModerationRules = async () => {
    if (!restaurantId) return;

    try {
      console.log("🔄 Carregando regras de moderação para:", restaurantId);

      // Para agora, vamos usar dados mock melhorados até implementar API completa
      const mockRules: ModerationRule[] = [
        {
          id: "1",
          name: "Palavras Proibidas",
          type: "blacklist",
          value: "explicit,inappropriate,offensive",
          isActive: true,
          createdAt: "2024-01-10T10:00:00Z",
        },
        {
          id: "2",
          name: "Gêneros Permitidos",
          type: "whitelist",
          value: "rock,pop,jazz,classical,blues",
          isActive: true,
          createdAt: "2024-01-10T10:05:00Z",
        },
        {
          id: "3",
          name: "Duração Máxima",
          type: "duration",
          value: "600",
          isActive: true,
          createdAt: "2024-01-10T10:10:00Z",
        },
      ];

      setModerationRules(mockRules);
    } catch (error) {
      console.error("Erro ao carregar regras:", error);
    }
  };

  const moderateSuggestion = async (
    suggestionId: string,
    action: "approve" | "reject",
    reason?: string
  ) => {
    if (!restaurantId) return;

    try {
      console.log(`🔍 Moderando sugestão ${suggestionId}:`, action, reason);

      const url = buildApiUrl(
        `/suggestions/${restaurantId}/${suggestionId}/${action}`
      );

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason:
            reason ||
            `Moderação ${
              action === "approve" ? "aprovada" : "rejeitada"
            } pelo administrador`,
        }),
      });

      if (!response.ok) {
        throw new Error(`Erro ao moderar sugestão: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ Moderação realizada:", result);

      // Remover da lista de pendentes
      setPendingSuggestions((prev) =>
        prev.filter((s) => s.id !== suggestionId)
      );
      toast.success(
        `Sugestão ${
          action === "approve" ? "aprovada" : "rejeitada"
        } com sucesso!`
      );

      // Recarregar lista para garantir sincronia
      setTimeout(() => {
        loadPendingSuggestions();
      }, 1000);
    } catch (error) {
      console.error("❌ Erro ao moderar sugestão:", error);
      toast.error("Erro ao moderar sugestão");
    }
  };

  const addModerationRule = async () => {
    if (!newRule.name.trim() || !newRule.value.trim()) {
      toast.error("Nome e valor da regra são obrigatórios");
      return;
    }

    const rule: ModerationRule = {
      id: Date.now().toString(),
      name: newRule.name,
      type: newRule.type,
      value: newRule.value,
      isActive: true,
      createdAt: new Date().toISOString(),
    };

    setModerationRules((prev) => [...prev, rule]);
    setNewRule({ name: "", type: "blacklist", value: "" });
    setShowAddRule(false);
    toast.success("Regra adicionada com sucesso!");
  };

  const toggleRule = (ruleId: string) => {
    setModerationRules((prev) =>
      prev.map((rule) =>
        rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
      )
    );
  };

  const deleteRule = (ruleId: string) => {
    setModerationRules((prev) => prev.filter((rule) => rule.id !== ruleId));
    toast.success("Regra removida");
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      default:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "approve":
        return "text-green-600 dark:text-green-400";
      case "reject":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-yellow-600 dark:text-yellow-400";
    }
  };

  const renderPendingSuggestions = () => (
    <div className="space-y-4">
      {pendingSuggestions.length === 0 ? (
        <div className="text-center py-12">
          <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            Nenhuma sugestão pendente de moderação
          </p>
        </div>
      ) : (
        pendingSuggestions.map((suggestion) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <img
                  src={suggestion.thumbnailUrl}
                  alt={suggestion.title}
                  className="w-16 h-12 object-cover rounded"
                />

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {suggestion.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {suggestion.artist} • {suggestion.duration}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {suggestion.submittedBy} •{" "}
                    {new Date(suggestion.submittedAt).toLocaleString("pt-BR")}
                  </p>

                  {suggestion.flags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {suggestion.flags.map((flag) => (
                        <span
                          key={flag}
                          className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 text-xs rounded-full"
                        >
                          {flag}
                        </span>
                      ))}
                    </div>
                  )}

                  {suggestion.autoModerationResult && (
                    <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                      <p
                        className={`font-medium ${getActionColor(
                          suggestion.autoModerationResult.action
                        )}`}
                      >
                        Auto-moderação: {suggestion.autoModerationResult.action}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {suggestion.autoModerationResult.reason} (
                        {suggestion.autoModerationResult.confidence}% confiança)
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(
                    suggestion.riskLevel
                  )}`}
                >
                  {suggestion.riskLevel}
                </span>

                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {suggestion.votes} votos
                  </p>
                </div>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => moderateSuggestion(suggestion.id, "approve")}
                    className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg"
                    title="Aprovar"
                  >
                    <CheckCircle className="w-4 h-4" />
                  </button>

                  <button
                    onClick={() => moderateSuggestion(suggestion.id, "reject")}
                    className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                    title="Rejeitar"
                  >
                    <XCircle className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))
      )}
    </div>
  );

  const renderModerationRules = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Regras de Moderação</h3>
        <button
          onClick={() => setShowAddRule(!showAddRule)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Nova Regra</span>
        </button>
      </div>

      {showAddRule && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <input
              type="text"
              placeholder="Nome da regra"
              value={newRule.name}
              onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800"
            />

            <select
              value={newRule.type}
              onChange={(e) =>
                setNewRule({ ...newRule, type: e.target.value as any })
              }
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800"
            >
              <option value="blacklist">Lista Negra</option>
              <option value="whitelist">Lista Branca</option>
              <option value="duration">Duração</option>
              <option value="explicit">Conteúdo Explícito</option>
              <option value="genre">Gênero</option>
            </select>

            <input
              type="text"
              placeholder="Valor (separado por vírgulas)"
              value={newRule.value}
              onChange={(e) =>
                setNewRule({ ...newRule, value: e.target.value })
              }
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800"
            />
          </div>

          <div className="flex space-x-2">
            <button
              onClick={addModerationRule}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Salvar</span>
            </button>
            <button
              onClick={() => setShowAddRule(false)}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              Cancelar
            </button>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {moderationRules.map((rule) => (
          <div
            key={rule.id}
            className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
          >
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {rule.name}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Tipo: {rule.type} • Valor: {rule.value}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                Criada em {new Date(rule.createdAt).toLocaleDateString("pt-BR")}
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={rule.isActive}
                  onChange={() => toggleRule(rule.id)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>

              <button
                onClick={() => deleteRule(rule.id)}
                className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Configurações de Moderação</h3>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Aprovação Automática
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Aprovar automaticamente sugestões com muitos votos positivos
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={moderationSettings.autoApprove}
              onChange={(e) =>
                setModerationSettings((prev) => ({
                  ...prev,
                  autoApprove: e.target.checked,
                }))
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Rejeição Automática
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Rejeitar automaticamente sugestões com muitos votos negativos
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={moderationSettings.autoReject}
              onChange={(e) =>
                setModerationSettings((prev) => ({
                  ...prev,
                  autoReject: e.target.checked,
                }))
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Votos para Aprovação Automática
            </label>
            <input
              type="number"
              value={moderationSettings.minVotesForAutoApproval}
              onChange={(e) =>
                setModerationSettings((prev) => ({
                  ...prev,
                  minVotesForAutoApproval: parseInt(e.target.value),
                }))
              }
              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Duração Máxima (segundos)
            </label>
            <input
              type="number"
              value={moderationSettings.maxDuration}
              onChange={(e) =>
                setModerationSettings((prev) => ({
                  ...prev,
                  maxDuration: parseInt(e.target.value),
                }))
              }
              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700"
            />
          </div>
        </div>
      </div>
    </div>
  );

  // Validação do contexto do restaurante
  if (!restaurantId) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-amber-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Restaurante não encontrado
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Selecione um restaurante para acessar a moderação.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Moderação Avançada
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Sistema inteligente de moderação de conteúdo
          </p>
        </div>

        <button
          onClick={loadPendingSuggestions}
          className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: "pending", name: "Pendentes", icon: Clock },
            { id: "rules", name: "Regras", icon: Shield },
            { id: "settings", name: "Configurações", icon: Settings },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        {activeTab === "pending" && renderPendingSuggestions()}
        {activeTab === "rules" && renderModerationRules()}
        {activeTab === "settings" && renderSettings()}
      </div>
    </div>
  );
};

export default AdvancedModeration;
