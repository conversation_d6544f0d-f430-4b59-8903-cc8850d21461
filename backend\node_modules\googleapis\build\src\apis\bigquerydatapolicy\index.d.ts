/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { bigquerydatapolicy_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof bigquerydatapolicy_v1.Bigquerydatapolicy;
};
export declare function bigquerydatapolicy(version: 'v1'): bigquerydatapolicy_v1.Bigquerydatapolicy;
export declare function bigquerydatapolicy(options: bigquerydatapolicy_v1.Options): bigquerydatapolicy_v1.Bigquerydatapolicy;
declare const auth: AuthPlus;
export { auth };
export { bigquerydatapolicy_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
