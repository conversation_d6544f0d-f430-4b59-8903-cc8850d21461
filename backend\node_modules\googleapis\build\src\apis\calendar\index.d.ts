/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { calendar_v3 } from './v3';
export declare const VERSIONS: {
    v3: typeof calendar_v3.Calendar;
};
export declare function calendar(version: 'v3'): calendar_v3.Calendar;
export declare function calendar(options: calendar_v3.Options): calendar_v3.Calendar;
declare const auth: AuthPlus;
export { auth };
export { calendar_v3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
