import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
// import { Suggestion } from "./Suggestion";

@Entity("genres")
@Index(["name"], { unique: true })
@Index(["category"])
@Index(["isActive"])
export class Genre {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100, unique: true })
  name: string;

  @Column({ type: "varchar", length: 100 })
  displayName: string;

  @Column({ type: "varchar", length: 200, nullable: true })
  description: string;

  @Column({ type: "varchar", length: 50, default: "music" })
  category: "music" | "mood" | "energy" | "time" | "custom";

  @Column({ type: "varchar", length: 7, default: "#3B82F6" })
  color: string; // Cor hexadecimal

  @Column({ type: "varchar", length: 50, nullable: true })
  icon: string; // Nome do ícone

  @Column({ type: "json", nullable: true })
  metadata: {
    spotifyGenreId?: string;
    youtubeGenreId?: string;
    lastFmGenreId?: string;
    keywords?: string[];
    relatedGenres?: string[];
    popularArtists?: string[];
    characteristics?: string[];
  };

  @Column({ type: "integer", default: 0 })
  priority: number; // Para ordenação

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "boolean", default: false })
  isDefault: boolean; // Gêneros padrão do sistema

  @Column({ type: "integer", default: 0 })
  usageCount: number; // Quantas vezes foi usado

  @Column({ type: "timestamp", nullable: true })
  lastUsedAt: Date;

  @CreateDateColumn({ name: "createdAt" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updatedAt" })
  updatedAt: Date;

  // Relacionamentos removidos para evitar problemas de metadata

  // Métodos auxiliares
  incrementUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  getDisplayInfo() {
    return {
      id: this.id,
      name: this.name,
      displayName: this.displayName,
      category: this.category,
      color: this.color,
      icon: this.icon,
      isActive: this.isActive,
      usageCount: this.usageCount,
    };
  }

  static getDefaultGenres(): Partial<Genre>[] {
    return [
      // Gêneros Musicais
      {
        name: "rock",
        displayName: "Rock",
        description: "Rock classico e moderno",
        category: "music",
        color: "#DC2626",
        icon: "Music2",
        isDefault: true,
        priority: 1,
        metadata: {
          keywords: ["rock", "guitar", "drums", "band"],
          characteristics: ["energetic", "guitar-driven", "rhythmic"],
        },
      },
      {
        name: "pop",
        displayName: "Pop",
        description: "Musica popular contemporanea",
        category: "music",
        color: "#EC4899",
        icon: "Mic",
        isDefault: true,
        priority: 2,
        metadata: {
          keywords: ["pop", "mainstream", "catchy", "commercial"],
          characteristics: ["catchy", "mainstream", "accessible"],
        },
      },
      {
        name: "sertanejo",
        displayName: "Sertanejo",
        description: "Musica sertaneja brasileira",
        category: "music",
        color: "#D97706",
        icon: "Music",
        isDefault: true,
        priority: 3,
        metadata: {
          keywords: ["sertanejo", "country", "brasil", "dupla"],
          characteristics: ["romantic", "storytelling", "acoustic"],
        },
      },
      {
        name: "mpb",
        displayName: "MPB",
        description: "Musica Popular Brasileira",
        category: "music",
        color: "#059669",
        icon: "Heart",
        isDefault: true,
        priority: 4,
        metadata: {
          keywords: ["mpb", "brasil", "poesia", "cultura"],
          characteristics: ["poetic", "cultural", "sophisticated"],
        },
      },
      {
        name: "funk",
        displayName: "Funk",
        description: "Funk brasileiro e internacional",
        category: "music",
        color: "#EA580C",
        icon: "Volume2",
        isDefault: true,
        priority: 5,
        metadata: {
          keywords: ["funk", "beat", "dance", "rhythm"],
          characteristics: ["danceable", "rhythmic", "energetic"],
        },
      },
      {
        name: "eletronica",
        displayName: "Eletronica",
        description: "Musica eletronica e EDM",
        category: "music",
        color: "#7C3AED",
        icon: "Zap",
        isDefault: true,
        priority: 6,
        metadata: {
          keywords: ["electronic", "edm", "synth", "digital"],
          characteristics: ["synthetic", "danceable", "modern"],
        },
      },
      {
        name: "jazz",
        displayName: "Jazz",
        description: "Jazz classico e contemporaneo",
        category: "music",
        color: "#1F2937",
        icon: "Music3",
        isDefault: true,
        priority: 7,
        metadata: {
          keywords: ["jazz", "improvisation", "swing", "blues"],
          characteristics: ["improvisational", "sophisticated", "rhythmic"],
        },
      },
      {
        name: "reggae",
        displayName: "Reggae",
        description: "Reggae e musica jamaicana",
        category: "music",
        color: "#16A34A",
        icon: "Music4",
        isDefault: true,
        priority: 8,
        metadata: {
          keywords: ["reggae", "jamaica", "rastafari", "island"],
          characteristics: ["relaxed", "spiritual", "rhythmic"],
        },
      },

      // Humores
      {
        name: "happy",
        displayName: "Alegre",
        description: "Musicas alegres e animadas",
        category: "mood",
        color: "#FCD34D",
        icon: "Smile",
        isDefault: true,
        priority: 10,
      },
      {
        name: "sad",
        displayName: "Melancolico",
        description: "Musicas tristes e melancolicas",
        category: "mood",
        color: "#6B7280",
        icon: "CloudRain",
        isDefault: true,
        priority: 11,
      },
      {
        name: "romantic",
        displayName: "Romantico",
        description: "Musicas romanticas",
        category: "mood",
        color: "#F472B6",
        icon: "Heart",
        isDefault: true,
        priority: 12,
      },
      {
        name: "energetic",
        displayName: "Energetico",
        description: "Musicas energeticas",
        category: "mood",
        color: "#EF4444",
        icon: "Zap",
        isDefault: true,
        priority: 13,
      },
      {
        name: "calm",
        displayName: "Calmo",
        description: "Músicas calmas e relaxantes",
        category: "mood",
        color: "#06B6D4",
        icon: "Waves",
        isDefault: true,
        priority: 14,
      },

      // Energia
      {
        name: "high_energy",
        displayName: "Alta Energia",
        description: "Músicas de alta energia",
        category: "energy",
        color: "#DC2626",
        icon: "Zap",
        isDefault: true,
        priority: 20,
      },
      {
        name: "medium_energy",
        displayName: "Média Energia",
        description: "Músicas de energia moderada",
        category: "energy",
        color: "#F59E0B",
        icon: "Activity",
        isDefault: true,
        priority: 21,
      },
      {
        name: "low_energy",
        displayName: "Baixa Energia",
        description: "Músicas de baixa energia",
        category: "energy",
        color: "#3B82F6",
        icon: "Headphones",
        isDefault: true,
        priority: 22,
      },

      // Horário
      {
        name: "morning",
        displayName: "Manhã",
        description: "Músicas para manhã",
        category: "time",
        color: "#FCD34D",
        icon: "Sun",
        isDefault: true,
        priority: 30,
      },
      {
        name: "afternoon",
        displayName: "Tarde",
        description: "Músicas para tarde",
        category: "time",
        color: "#F97316",
        icon: "Coffee",
        isDefault: true,
        priority: 31,
      },
      {
        name: "evening",
        displayName: "Noite",
        description: "Músicas para noite",
        category: "time",
        color: "#6366F1",
        icon: "Moon",
        isDefault: true,
        priority: 32,
      },
    ];
  }
}
