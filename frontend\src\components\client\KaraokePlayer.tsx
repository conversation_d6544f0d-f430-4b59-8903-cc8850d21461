import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Play, Pause, Volume2, VolumeX, Mic, X, RotateCcw, Settings, Maximize, Minimize } from "lucide-react";
import { toast } from "react-hot-toast";
import YouTube, { YouTubeProps } from "react-youtube";
import { buildApiUrl } from "@/config/api";

interface LyricsLine {
  time: number;
  text: string;
  duration?: number;
}

interface LyricsData {
  id: string;
  title: string;
  artist: string;
  duration: number;
  language: string;
  lines: LyricsLine[];
  hasTimestamps: boolean;
}

interface KaraokePlayerProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    id: string;
    title: string;
    artist: string;
    thumbnailUrl?: string;
    duration?: number;
    youtubeVideoId: string;
  };
  sessionId: string;
  onVoteRequest?: () => void;
}

const KaraokePlayer: React.FC<KaraokePlayerProps> = ({
  isOpen,
  onClose,
  suggestion,
  sessionId,
  onVoteRequest,
}) => {
  const [lyrics, setLyrics] = useState<LyricsData | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(24);
  const [highlightColor, setHighlightColor] = useState("#3B82F6");
  const [loading, setLoading] = useState(false);
  const [currentLineIndex, setCurrentLineIndex] = useState(-1);
  const [progress, setProgress] = useState(0);

  const playerRef = useRef<YouTube>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const loadLyrics = useCallback(async () => {
    if (!suggestion.youtubeVideoId) {
      toast.error("ID do vídeo não fornecido");
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(
        buildApiUrl(`/lyrics/search?title=${encodeURIComponent(suggestion.title)}&artist=${encodeURIComponent(suggestion.artist)}&youtubeVideoId=${suggestion.youtubeVideoId}`)
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.lyrics) {
          setLyrics(data.lyrics);
          toast.success("Letras carregadas! 🎤");
        } else {
          // Fallback para letras de teste
          const testResponse = await fetch(
            buildApiUrl(`/lyrics/test?title=${encodeURIComponent(suggestion.title)}&artist=${encodeURIComponent(suggestion.artist)}`)
          );
          if (testResponse.ok) {
            const testData = await testResponse.json();
            setLyrics(testData.lyrics);
            toast("Usando letras de demonstração 🎵", { icon: "ℹ️" });
          } else {
            throw new Error("Letras de teste não disponíveis");
          }
        }
      } else {
        throw new Error("Erro na resposta da API de letras");
      }
    } catch (error) {
      console.error("Erro ao carregar letras:", error);
      toast.error("Não foi possível carregar as letras");
    } finally {
      setLoading(false);
    }
  }, [suggestion.title, suggestion.artist, suggestion.youtubeVideoId]);

  const updateCurrentLine = useCallback((time: number) => {
    if (!lyrics) return;

    let newIndex = -1;
    let newProgress = 0;

    for (let i = 0; i < lyrics.lines.length; i++) {
      if (time >= lyrics.lines[i].time) {
        newIndex = i;
      } else {
        break;
      }
    }

    if (newIndex >= 0 && newIndex < lyrics.lines.length - 1) {
      const currentLine = lyrics.lines[newIndex];
      const nextLine = lyrics.lines[newIndex + 1];
      const lineDuration = nextLine.time - currentLine.time;
      const elapsed = time - currentLine.time;
      newProgress = Math.min(1, Math.max(0, elapsed / lineDuration));
    }

    setCurrentLineIndex(newIndex);
    setProgress(newProgress);
  }, [lyrics]);

  const syncLyrics = useCallback(() => {
    if (!isPlaying || !playerRef.current || !lyrics) return;

    const player = playerRef.current.getInternalPlayer();
    player.getCurrentTime().then((time: number) => {
      setCurrentTime(time);
      updateCurrentLine(time);
    });
  }, [isPlaying, lyrics, updateCurrentLine]);

  const togglePlay = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isPlaying) {
        player.pauseVideo();
      } else {
        player.playVideo();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const toggleMute = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isMuted) {
        player.unMute();
        player.setVolume(50);
      } else {
        player.mute();
      }
      setIsMuted(!isMuted);
    }
  }, [isMuted]);

  const restart = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      player.seekTo(0);
      if (!isPlaying) {
        player.playVideo();
        setIsPlaying(true);
      }
    }
    setCurrentTime(0);
    setCurrentLineIndex(-1);
    setProgress(0);
  }, [isPlaying]);

  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;
    if (!isFullscreen) {
      containerRef.current.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleYouTubeReady: YouTubeProps['onReady'] = useCallback((event) => {
    event.target.setVolume(isMuted ? 0 : 50);
  }, [isMuted]);

  const handleYouTubeStateChange: YouTubeProps['onStateChange'] = useCallback((event) => {
    if (event.data === YouTube.PlayerState.PLAYING) {
      setIsPlaying(true);
    } else if (event.data === YouTube.PlayerState.PAUSED) {
      setIsPlaying(false);
    } else if (event.data === YouTube.PlayerState.ENDED) {
      setIsPlaying(false);
      setCurrentTime(0);
      setCurrentLineIndex(-1);
      setProgress(0);
    }
  }, []);

  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }, []);

  const getCurrentLine = useCallback((): LyricsLine | null => {
    if (!lyrics || currentLineIndex < 0) return null;
    return lyrics.lines[currentLineIndex] || null;
  }, [lyrics, currentLineIndex]);

  const getNextLine = useCallback((): LyricsLine | null => {
    if (!lyrics || currentLineIndex < 0 || currentLineIndex >= lyrics.lines.length - 1) return null;
    return lyrics.lines[currentLineIndex + 1] || null;
  }, [lyrics, currentLineIndex]);

  const getContextLines = useCallback((): LyricsLine[] => {
    if (!lyrics || currentLineIndex < 0) return [];
    const start = Math.max(0, currentLineIndex - 2);
    const end = Math.min(lyrics.lines.length, currentLineIndex + 3);
    return lyrics.lines.slice(start, end);
  }, [lyrics, currentLineIndex]);

  useEffect(() => {
    if (isOpen && suggestion.youtubeVideoId) {
      loadLyrics();
    }
    return () => {
      setLyrics(null);
      setCurrentTime(0);
      setCurrentLineIndex(-1);
      setProgress(0);
      setIsPlaying(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isOpen, suggestion.youtubeVideoId, loadLyrics]);

  useEffect(() => {
    if (isPlaying && lyrics) {
      timerRef.current = setInterval(syncLyrics, 50);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isPlaying, lyrics, syncLyrics]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={`fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 ${isFullscreen ? "p-0" : "p-4"}`}
        ref={containerRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="karaoke-title"
      >
        <header className="flex items-center justify-between p-4 sm:p-6 bg-black/30 backdrop-blur-md">
          <div className="flex items-center gap-4">
            {suggestion.thumbnailUrl && (
              <img
                src={suggestion.thumbnailUrl}
                alt={`Capa de ${suggestion.title}`}
                className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover"
                aria-hidden="true"
              />
            )}
            <div>
              <h2 id="karaoke-title" className="text-xl sm:text-2xl font-bold text-white">{suggestion.title}</h2>
              <p className="text-sm sm:text-lg text-gray-300">{suggestion.artist}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              aria-label={showSettings ? "Fechar configurações" : "Abrir configurações"}
            >
              <Settings className="w-5 h-5" aria-hidden="true" />
            </button>
            <button
              onClick={toggleFullscreen}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              aria-label={isFullscreen ? "Sair da tela cheia" : "Entrar na tela cheia"}
            >
              {isFullscreen ? <Minimize className="w-5 h-5" aria-hidden="true" /> : <Maximize className="w-5 h-5" aria-hidden="true" />}
            </button>
            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              aria-label="Fechar player de karaokê"
            >
              <X className="w-5 h-5" aria-hidden="true" />
            </button>
          </div>
        </header>

        {showSettings && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-black/50 backdrop-blur-md p-4 mx-4 sm:mx-6 rounded-lg"
          >
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6">
              <div className="flex items-center gap-2">
                <span className="text-white text-sm">Tamanho da fonte:</span>
                <input
                  type="range"
                  min="16"
                  max="48"
                  value={fontSize}
                  onChange={(e) => setFontSize(Number(e.target.value))}
                  className="w-20"
                  aria-label="Ajustar tamanho da fonte"
                />
                <span className="text-white text-sm">{fontSize}px</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-white text-sm">Cor de destaque:</span>
                <input
                  type="color"
                  value={highlightColor}
                  onChange={(e) => setHighlightColor(e.target.value)}
                  className="w-8 h-8 rounded border-none"
                  aria-label="Escolher cor de destaque"
                />
              </div>
            </div>
          </motion.div>
        )}

        <div className="flex-1 flex flex-col items-center justify-center p-4 sm:p-6">
          {suggestion.youtubeVideoId && (
            <div className="w-full max-w-4xl mb-4 sm:mb-6">
              <YouTube
                videoId={suggestion.youtubeVideoId}
                opts={{
                  width: "100%",
                  height: isFullscreen ? "400" : "300",
                  playerVars: {
                    autoplay: 0,
                    controls: 0,
                    rel: 0,
                    showinfo: 0,
                    modestbranding: 1,
                  },
                }}
                onReady={handleYouTubeReady}
                onStateChange={handleYouTubeStateChange}
                ref={playerRef}
                className="rounded-lg overflow-hidden"
              />
            </div>
          )}

          {loading ? (
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4" aria-hidden="true" />
              <p className="text-white text-lg sm:text-xl">Carregando letras...</p>
            </div>
          ) : lyrics ? (
            <div className="text-center max-w-4xl w-full">
              <motion.div
                key={currentLineIndex}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
                className="mb-6 sm:mb-8"
                aria-live="polite"
              >
                <div
                  className="text-white font-bold leading-relaxed mb-4"
                  style={{
                    fontSize: `${fontSize}px`,
                    color: getCurrentLine() ? highlightColor : "white",
                    textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                  }}
                >
                  {getCurrentLine()?.text || "🎵 Aguardando..."}
                </div>
                {getCurrentLine() && (
                  <div className="w-full bg-white/20 rounded-full h-2 mb-4">
                    <div
                      className="h-2 rounded-full transition-all duration-100"
                      style={{ width: `${progress * 100}%`, backgroundColor: highlightColor }}
                      role="progressbar"
                      aria-valuenow={progress * 100}
                      aria-valuemin={0}
                      aria-valuemax={100}
                    />
                  </div>
                )}
              </motion.div>
              {getNextLine() && (
                <div
                  className="text-gray-300 opacity-60 mb-6 sm:mb-8"
                  style={{ fontSize: `${fontSize * 0.8}px` }}
                >
                  Próxima: {getNextLine()?.text}
                </div>
              )}
              <div className="space-y-2 opacity-40">
                {getContextLines().map((line, index) => {
                  const isCurrentLine = lyrics.lines.indexOf(line) === currentLineIndex;
                  if (isCurrentLine) return null;
                  return (
                    <div
                      key={index}
                      className="text-gray-400"
                      style={{ fontSize: `${fontSize * 0.6}px` }}
                    >
                      {line.text}
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center">
              <Mic className="w-16 h-16 sm:w-24 sm:h-24 text-white opacity-50 mx-auto mb-4" aria-hidden="true" />
              <p className="text-white text-lg sm:text-xl mb-4">🎤 Modo Karaokê Ativo!</p>
              <p className="text-gray-300 text-sm sm:text-base mb-2">Letras sincronizadas não disponíveis.</p>
              <p className="text-gray-300 text-sm sm:text-base">Cante junto com o vídeo! 🎵</p>
            </div>
          )}
        </div>

        <footer className="bg-black/30 backdrop-blur-md p-4 sm:p-6">
          {lyrics && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-white text-sm mb-2">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(suggestion.duration || lyrics.duration)}</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div
                  className="bg-white h-2 rounded-full transition-all duration-100"
                  style={{ width: `${(currentTime / (suggestion.duration || lyrics.duration)) * 100}%` }}
                  role="progressbar"
                  aria-valuenow={(currentTime / (suggestion.duration || lyrics.duration)) * 100}
                  aria-valuemin={0}
                  aria-valuemax={100}
                />
              </div>
            </div>
          )}
          <div className="flex items-center justify-center gap-4 sm:gap-6">
            <button
              onClick={restart}
              className="p-2 sm:p-3 bg-white/20 text-white rounded-full hover:bg-white/30 transition-colors"
              aria-label="Reiniciar música"
            >
              <RotateCcw className="w-5 h-5" aria-hidden="true" />
            </button>
            <button
              onClick={togglePlay}
              className="p-3 sm:p-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              aria-label={isPlaying ? "Pausar música" : "Tocar música"}
            >
              {isPlaying ? <Pause className="w-6 h-6" aria-hidden="true" /> : <Play className="w-6 h-6" aria-hidden="true" />}
            </button>
            <button
              onClick={toggleMute}
              className="p-2 sm:p-3 bg-white/20 text-white rounded-full hover:bg-white/30 transition-colors"
              aria-label={isMuted ? "Ativar som" : "Silenciar som"}
            >
              {isMuted ? <VolumeX className="w-5 h-5" aria-hidden="true" /> : <Volume2 className="w-5 h-5" aria-hidden="true" />}
            </button>
            {onVoteRequest && (
              <button
                onClick={onVoteRequest}
                className="p-2 sm:p-3 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors"
                aria-label="Solicitar votação da performance"
              >
                <Heart className="w-5 h-5" aria-hidden="true" />
              </button>
            )}
          </div>
          <div className="flex items-center justify-center gap-4 sm:gap-6 mt-4 text-white text-xs sm:text-sm">
            <div className="flex items-center gap-2">
              <Mic className="w-4 h-4" aria-hidden="true" />
              <span>Cante Comigo</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" aria-hidden="true" />
              <span>Karaokê Interativo</span>
            </div>
            {lyrics?.hasTimestamps && (
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4" aria-hidden="true" />
                <span>Sincronizado</span>
              </div>
            )}
          </div>
        </footer>
      </motion.div>
    </AnimatePresence>
  );
};

export default KaraokePlayer;
