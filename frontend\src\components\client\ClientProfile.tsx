import React, { useState, useEffect, useCallback, ChangeEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, User, Music, ThumbsUp, Trophy, Clock, Share2, Download, Settings, LogOut, Edit3, Save, Upload } from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "@/config/api";
import BadgeSystem from "./BadgeSystem";

interface UserStats {
  totalSuggestions: number;
  totalVotes: number;
  approvedSuggestions: number;
  consecutiveDays: number;
  topVotedSongs: number;
  averageVotesPerSuggestion: number;
  favoriteGenres: string[];
  sessionDuration: number;
  pageViews: number;
  engagementLevel: string;
}

interface UserProfile {
  name: string;
  avatar?: string;
  joinedAt: string;
  level: number;
  experience: number;
  nextLevelExp: number;
  title: string;
  preferences: {
    favoriteGenres: string[];
    notifications: boolean;
    autoShare: boolean;
  };
}

interface ClientProfileProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantId: string;
  sessionId: string;
}

const ClientProfile: React.FC<ClientProfileProps> = ({ isOpen, onClose, restaurantId, sessionId }) => {
  const [activeTab, setActiveTab] = useState<"profile" | "stats" | "badges" | "settings">("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState<UserProfile>({
    name: "",
    avatar: "",
    joinedAt: new Date().toISOString(),
    level: 1,
    experience: 0,
    nextLevelExp: 100,
    title: "Novo Ouvinte",
    preferences: { favoriteGenres: [], notifications: true, autoShare: false },
  });
  const [stats, setStats] = useState<UserStats>({
    totalSuggestions: 0,
    totalVotes: 0,
    approvedSuggestions: 0,
    consecutiveDays: 1,
    topVotedSongs: 0,
    averageVotesPerSuggestion: 0,
    favoriteGenres: [],
    sessionDuration: 0,
    pageViews: 0,
    engagementLevel: "Iniciante",
  });

  const loadProfile = useCallback(async () => {
    setLoading(true);
    try {
      const savedProfile = localStorage.getItem(`clientProfile_${sessionId}`);
      if (savedProfile) {
        setProfile(JSON.parse(savedProfile));
      } else {
        const response = await fetch(buildApiUrl(`/client/${sessionId}/profile`), {
          headers: { "X-Session-ID": sessionId },
        });
        if (response.ok) {
          const data = await response.json();
          setProfile(data.profile);
          localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(data.profile));
        } else {
          throw new Error("Failed to fetch profile");
        }
      }
    } catch (error) {
      console.error("Error loading profile:", error);
      const defaultProfile: UserProfile = {
        name: `Cliente ${sessionId.slice(0, 8)}`,
        avatar: "",
        joinedAt: new Date().toISOString(),
        level: 1,
        experience: 0,
        nextLevelExp: 100,
        title: "Novo Ouvinte",
        preferences: { favoriteGenres: [], notifications: true, autoShare: false },
      };
      setProfile(defaultProfile);
      localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(defaultProfile));
      toast.error("Error loading profile, using default");
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  const loadStats = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(buildApiUrl(`/client/${sessionId}/stats`), {
        headers: { "X-Session-ID": sessionId },
      });
      if (response.ok) {
        const { stats } = await response.json();
        setStats(stats);
      } else {
        throw new Error("Failed to fetch stats");
      }
    } catch (error) {
      console.error("Error loading stats:", error);
      setStats({
        totalSuggestions: 5,
        totalVotes: 12,
        approvedSuggestions: 3,
        consecutiveDays: 2,
        topVotedSongs: 1,
        averageVotesPerSuggestion: 2.4,
        favoriteGenres: ["Pop", "Rock"],
        sessionDuration: 45,
        pageViews: 8,
        engagementLevel: "Ativo",
      });
      toast.error("Error loading stats, using mock data");
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  const saveProfile = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl(`/client/${sessionId}/profile`), {
        method: "PUT",
        headers: { "Content-Type": "application/json", "X-Session-ID": sessionId },
        body: JSON.stringify(profile),
      });
      if (response.ok) {
        localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(profile));
        setIsEditing(false);
        toast.success("Perfil salvo com sucesso!");
      } else {
        throw new Error("Failed to save profile");
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      toast.error("Erro ao salvar perfil");
    }
  }, [profile, sessionId]);

  const handleAvatarUpload = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = () => {
        setProfile((prev) => ({ ...prev, avatar: reader.result as string }));
      };
      reader.readAsDataURL(file);
    } else {
      toast.error("Por favor, selecione uma imagem válida");
    }
  }, []);

  const exportData = useCallback(() => {
    const data = { profile, stats, exportedAt: new Date().toISOString() };
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `meu-perfil-musical-${sessionId.slice(0, 8)}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Dados exportados com sucesso!");
  }, [profile, stats, sessionId]);

  const shareProfile = useCallback(async () => {
    const shareData = {
      title: `Perfil Musical de ${profile.name}`,
      text: `Confira meu perfil musical! Nível ${profile.level}, ${stats.totalSuggestions} sugestões, ${stats.approvedSuggestions} aprovadas.`,
      url: window.location.href,
    };
    if (navigator.share) {
      try {
        await navigator.share(shareData);
        toast.success("Perfil compartilhado!");
      } catch (error) {
        console.error("Error sharing profile:", error);
        toast.error("Erro ao compartilhar perfil");
      }
    } else {
      navigator.clipboard.writeText(`${shareData.text} ${shareData.url}`);
      toast.success("Link copiado para a área de transferência!");
    }
  }, [profile.name, profile.level, stats.totalSuggestions, stats.approvedSuggestions]);

  const getLevelProgress = useCallback(() => {
    return Math.min((profile.experience / profile.nextLevelExp) * 100, 100);
  }, [profile.experience, profile.nextLevelExp]);

  const getEngagementColor = useCallback((level: string) => {
    const colorMap: { [key: string]: string } = {
      Iniciante: "text-gray-600 bg-gray-100 dark:bg-gray-700",
      Ativo: "text-blue-600 bg-blue-100 dark:bg-blue-900/30",
      Engajado: "text-green-600 bg-green-100 dark:bg-green-900/30",
      Expert: "text-purple-600 bg-purple-100 dark:bg-purple-900/30",
    };
    return colorMap[level] || colorMap.Iniciante;
  }, []);

  useEffect(() => {
    if (isOpen && sessionId) {
      loadProfile();
      loadStats();
    }
  }, [isOpen, sessionId, loadProfile, loadStats]);

  if (!isOpen) return null;

  const tabs = [
    { id: "profile", name: "Perfil", icon: User },
    { id: "stats", name: "Estatísticas", icon: BarChart3 },
    { id: "badges", name: "Conquistas", icon: Trophy },
    { id: "settings", name: "Configurações", icon: Settings },
  ];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="profile-modal-title"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          <header className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  {profile.avatar ? (
                    <img src={profile.avatar} alt={`Avatar de ${profile.name}`} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    <User className="w-8 h-8" aria-hidden="true" />
                  )}
                </div>
                <div>
                  <h2 id="profile-modal-title" className="text-xl sm:text-2xl font-bold">{profile.name}</h2>
                  <p className="text-blue-100 text-sm">{profile.title}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm">Nível {profile.level}</span>
                    <div className="w-24 h-2 bg-white/20 rounded-full">
                      <div
                        className="h-full bg-white rounded-full transition-all duration-300"
                        style={{ width: `${getLevelProgress()}%` }}
                        role="progressbar"
                        aria-valuenow={Math.round(getLevelProgress())}
                        aria-valuemin={0}
                        aria-valuemax={100}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={shareProfile}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  aria-label="Compartilhar perfil"
                >
                  <Share2 className="w-5 h-5" aria-hidden="true" />
                </button>
                <button
                  onClick={exportData}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  aria-label="Exportar dados do perfil"
                >
                  <Download className="w-5 h-5" aria-hidden="true" />
                </button>
                <button
                  onClick={onClose}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  aria-label="Fechar perfil"
                >
                  <X className="w-5 h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
          </header>
          <nav className="border-b border-gray-200 dark:border-gray-700">
            <div className="flex gap-6 sm:gap-8 px-4 sm:px-6 overflow-x-auto">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-3 sm:py-4 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? "border-blue-500 text-blue-600 dark:text-blue-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                    aria-label={`Navegar para aba ${tab.name}`}
                  >
                    <div className="flex items-center gap-2">
                      <IconComponent className="w-4 h-4" aria-hidden="true" />
                      <span>{tab.name}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </nav>
          <div className="p-4 sm:p-6 max-h-[60vh] overflow-y-auto">
            {loading ? (
              <div className="text-center py-8">
                <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" aria-hidden="true" />
                <p className="text-gray-500 dark:text-gray-400">Carregando perfil...</p>
              </div>
            ) : (
              <>
                {activeTab === "profile" && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nome</label>
                        {isEditing ? (
                          <input
                            type="text"
                            value={profile.name}
                            onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            aria-label="Editar nome do perfil"
                          />
                        ) : (
                          <p className="text-gray-900 dark:text-white">{profile.name}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nível de Engajamento</label>
                        <span
                          className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getEngagementColor(stats.engagementLevel)}`}
                        >
                          {stats.engagementLevel}
                        </span>
                      </div>
                      {isEditing && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Avatar</label>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleAvatarUpload}
                            className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200"
                            aria-label="Fazer upload de avatar"
                          />
                        </div>
                      )}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Membro desde</label>
                        <p className="text-gray-900 dark:text-white">
                          {new Date(profile.joinedAt).toLocaleDateString("pt-BR", {
                            day: "2-digit",
                            month: "long",
                            year: "numeric",
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Resumo da Atividade</h3>
                      {isEditing ? (
                        <button
                          onClick={saveProfile}
                          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                          aria-label="Salvar alterações do perfil"
                        >
                          <Save className="w-4 h-4" aria-hidden="true" />
                          <span>Salvar</span>
                        </button>
                      ) : (
                        <button
                          onClick={() => setIsEditing(true)}
                          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                          aria-label="Editar perfil"
                        >
                          <Edit3 className="w-4 h-4" aria-hidden="true" />
                          <span>Editar</span>
                        </button>
                      )}
                    </div>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                      {[
                        { icon: Music, value: stats.totalSuggestions, label: "Sugestões", color: "text-blue-600", bg: "bg-blue-50 dark:bg-blue-900/20" },
                        { icon: ThumbsUp, value: stats.totalVotes, label: "Votos", color: "text-green-600", bg: "bg-green-50 dark:bg-green-900/20" },
                        { icon: Trophy, value: stats.approvedSuggestions, label: "Aprovadas", color: "text-purple-600", bg: "bg-purple-50 dark:bg-purple-900/20" },
                        { icon: Clock, value: stats.sessionDuration, label: "Minutos", color: "text-orange-600", bg: "bg-orange-50 dark:bg-orange-900/20" },
                      ].map((item) => (
                        <div key={item.label} className={`p-4 rounded-lg text-center ${item.bg}`}>
                          <item.icon className={`w-6 h-6 ${item.color} mx-auto mb-2`} aria-hidden="true" />
                          <p className={`text-xl font-bold ${item.color}`}>{item.value}</p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">{item.label}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {activeTab === "stats" && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Estatísticas Detalhadas</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Sugestões Aprovadas</span>
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.approvedSuggestions}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Dias Consecutivos</span>
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.consecutiveDays}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Músicas Top Votadas</span>
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.topVotedSongs}</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Média de Votos</span>
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.averageVotesPerSuggestion.toFixed(1)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Visualizações</span>
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.pageViews}</span>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">Gêneros Favoritos</span>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {stats.favoriteGenres.length ? (
                              stats.favoriteGenres.map((genre, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-xs sm:text-sm"
                                >
                                  {genre}
                                </span>
                              ))
                            ) : (
                              <span className="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">Nenhum gênero favorito</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {activeTab === "badges" && (
                  <BadgeSystem restaurantId={restaurantId} sessionId={sessionId} />
                )}
                {activeTab === "settings" && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Configurações</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">Notificações</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Receber notificações sobre atividades</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={profile.preferences.notifications}
                            onChange={(e) =>
                              setProfile({
                                ...profile,
                                preferences: { ...profile.preferences, notifications: e.target.checked },
                              })
                            }
                            className="sr-only peer"
                            aria-label="Ativar/desativar notificações"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">Compartilhamento Automático</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Compartilhar conquistas automaticamente</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={profile.preferences.autoShare}
                            onChange={(e) =>
                              setProfile({
                                ...profile,
                                preferences: { ...profile.preferences, autoShare: e.target.checked },
                              })
                            }
                            className="sr-only peer"
                            aria-label="Ativar/desativar compartilhamento automático"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => {
                          localStorage.removeItem(`clientProfile_${sessionId}`);
                          toast.success("Dados limpos com sucesso!");
                          onClose();
                        }}
                        className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        aria-label="Limpar dados do perfil"
                      >
                        <LogOut className="w-4 h-4" aria-hidden="true" />
                        <span>Limpar Dados</span>
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ClientProfile;
