/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { apihub_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof apihub_v1.Apihub;
};
export declare function apihub(version: 'v1'): apihub_v1.Apihub;
export declare function apihub(options: apihub_v1.Options): apihub_v1.Apihub;
declare const auth: AuthPlus;
export { auth };
export { apihub_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
