import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  X,
  TrendingUp,
  History,
  Clock,
  Music,
  Play,
  Plus,
  Loader2,
  Filter,
  Share2,
  Heart,
  Star,
  Volume2,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { apiService } from "../../services/api";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName?: string;
  viewCount?: number;
  publishedAt?: string;
  genre?: string;
  mood?: string;
}

interface SearchSuggestion {
  id: string;
  query: string;
  type: "history" | "trending" | "suggestion" | "genre" | "artist";
  count?: number;
  icon?: React.ComponentType<any>;
}

interface EnhancedSearchProps {
  onSearch: (query: string) => void;
  onSongSelect: (song: Song) => void;
  onSuggestionCreated?: () => void;
  restaurantId: string;
  placeholder?: string;
  showFilters?: boolean;
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  onSearch,
  onSongSelect,
  onSuggestionCreated,
  restaurantId,
  placeholder = "Buscar música, artista ou álbum...",
  showFilters = true,
}) => {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimeout = useRef<NodeJS.Timeout>();

  // Carregar histórico de busca
  useEffect(() => {
    const history = localStorage.getItem(`searchHistory_${restaurantId}`);
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, [restaurantId]);

  // Salvar no histórico
  const saveToHistory = useCallback(
    (searchQuery: string) => {
      const newHistory = [
        searchQuery,
        ...searchHistory.filter((h) => h !== searchQuery),
      ].slice(0, 10);
      setSearchHistory(newHistory);
      localStorage.setItem(
        `searchHistory_${restaurantId}`,
        JSON.stringify(newHistory)
      );
    },
    [searchHistory, restaurantId]
  );

  // Gerar sugestões
  const generateSuggestions = useCallback(
    (searchQuery: string) => {
      const suggestions: SearchSuggestion[] = [];

      if (!searchQuery.trim()) {
        // Mostrar histórico e trending quando não há busca
        searchHistory.slice(0, 5).forEach((item) => {
          suggestions.push({
            id: `history-${item}`,
            query: item,
            type: "history",
            icon: History,
          });
        });

        // Adicionar sugestões trending
        const trending = [
          "Sertanejo",
          "Rock Nacional",
          "Pop Internacional",
          "MPB",
          "Funk",
        ];
        trending.forEach((item) => {
          suggestions.push({
            id: `trending-${item}`,
            query: item,
            type: "trending",
            icon: TrendingUp,
          });
        });
      } else {
        // Sugestões baseadas na busca
        const genres = [
          "Rock",
          "Pop",
          "Sertanejo",
          "MPB",
          "Funk",
          "Eletrônica",
        ];
        const artists = [
          "Marília Mendonça",
          "Gusttavo Lima",
          "Anitta",
          "Caetano Veloso",
          "Legião Urbana",
        ];

        genres.forEach((genre) => {
          if (genre.toLowerCase().includes(searchQuery.toLowerCase())) {
            suggestions.push({
              id: `genre-${genre}`,
              query: genre,
              type: "genre",
              icon: Music,
            });
          }
        });

        artists.forEach((artist) => {
          if (artist.toLowerCase().includes(searchQuery.toLowerCase())) {
            suggestions.push({
              id: `artist-${artist}`,
              query: artist,
              type: "artist",
              icon: Star,
            });
          }
        });
      }

      setSuggestions(suggestions.slice(0, 8));
    },
    [searchHistory]
  );

  // Buscar músicas
  const searchSongs = useCallback(
    async (searchQuery: string) => {
      if (!searchQuery.trim()) return;

      try {
        setLoading(true);
        setShowResults(true);

        // Buscar na API
        try {
          const data = await apiService.client.get(
            `/restaurants/${restaurantId}/playlist?q=${encodeURIComponent(
              searchQuery
            )}`
          );
          setSearchResults(data.data.results || []);
        } catch (error) {
          // Fallback para resultados mock
          const mockResults: Song[] = [
            {
              id: "dQw4w9WgXcQ",
              title: "Never Gonna Give You Up",
              artist: "Rick Astley",
              duration: 213,
              formattedDuration: "3:33",
              thumbnailUrl:
                "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
              channelName: "Rick Astley",
              viewCount: 1200000000,
              publishedAt: "2009-10-25",
              genre: "Pop",
              mood: "Happy",
            },
            {
              id: "kJQP7kiw5Fk",
              title: "Despacito",
              artist: "Luis Fonsi ft. Daddy Yankee",
              duration: 281,
              formattedDuration: "4:41",
              thumbnailUrl:
                "https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg",
              channelName: "LuisFonsiVEVO",
              viewCount: 7800000000,
              publishedAt: "2017-01-12",
              genre: "Latin Pop",
              mood: "Energetic",
            },
          ].filter(
            (song) =>
              song.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              song.artist.toLowerCase().includes(searchQuery.toLowerCase())
          );

          setSearchResults(mockResults);
        }

        saveToHistory(searchQuery);
        onSearch(searchQuery);
      } catch (error) {
        console.error("Erro ao buscar músicas:", error);
        toast.error("Erro ao buscar músicas");
      } finally {
        setLoading(false);
      }
    },
    [restaurantId, saveToHistory, onSearch]
  );

  // Handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);

    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      generateSuggestions(value);
      setShowSuggestions(true);
    }, 300);
  };

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    setShowSuggestions(false);
    searchSongs(searchQuery);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.query);
    setShowSuggestions(false);
    handleSearch(suggestion.query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prev) => Math.min(prev + 1, suggestions.length - 1));
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prev) => Math.max(prev - 1, -1));
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (selectedIndex >= 0) {
        handleSuggestionClick(suggestions[selectedIndex]);
      } else {
        handleSearch();
      }
    } else if (e.key === "Escape") {
      setShowSuggestions(false);
      setShowResults(false);
    }
  };

  const clearSearch = () => {
    setQuery("");
    setSearchResults([]);
    setShowResults(false);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleSongAction = (
    song: Song,
    action: "suggest" | "preview" | "share"
  ) => {
    switch (action) {
      case "suggest":
        onSongSelect(song);
        toast.success(`"${song.title}" sugerida com sucesso!`);
        break;
      case "preview":
        // Implementar preview
        toast.info("Preview em desenvolvimento");
        break;
      case "share":
        // Implementar compartilhamento
        if (navigator.share) {
          navigator.share({
            title: song.title,
            text: `Confira "${song.title}" de ${song.artist}`,
            url: `https://youtube.com/watch?v=${song.id}`,
          });
        } else {
          navigator.clipboard.writeText(
            `https://youtube.com/watch?v=${song.id}`
          );
          toast.success("Link copiado!");
        }
        break;
    }
  };

  const filters = [
    { id: "rock", name: "Rock", color: "bg-red-100 text-red-800" },
    { id: "pop", name: "Pop", color: "bg-pink-100 text-pink-800" },
    {
      id: "sertanejo",
      name: "Sertanejo",
      color: "bg-yellow-100 text-yellow-800",
    },
    { id: "mpb", name: "MPB", color: "bg-green-100 text-green-800" },
    { id: "funk", name: "Funk", color: "bg-purple-100 text-purple-800" },
  ];

  return (
    <div className="relative w-full">
      {/* Barra de busca */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>

        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
            generateSuggestions(query);
            setShowSuggestions(true);
          }}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="block w-full pl-10 pr-20 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm"
        />

        <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
          {showFilters && (
            <button
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={`p-1.5 rounded-lg transition-colors ${
                activeFilters.length > 0
                  ? "bg-blue-100 text-blue-600"
                  : "text-gray-400 hover:text-gray-600"
              }`}
            >
              <Filter className="w-4 h-4" />
            </button>
          )}

          {query && (
            <button
              onClick={clearSearch}
              className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}

          {loading && (
            <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
          )}
        </div>
      </div>

      {/* Filtros */}
      <AnimatePresence>
        {showFiltersPanel && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 p-4 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50"
          >
            <div className="flex flex-wrap gap-2">
              {filters.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => {
                    const newFilters = activeFilters.includes(filter.id)
                      ? activeFilters.filter((f) => f !== filter.id)
                      : [...activeFilters, filter.id];
                    setActiveFilters(newFilters);
                  }}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all ${
                    activeFilters.includes(filter.id)
                      ? filter.color
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {filter.name}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sugestões de busca */}
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-40 max-h-80 overflow-y-auto"
          >
            {suggestions.map((suggestion, index) => {
              const IconComponent = suggestion.icon || Music;
              return (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-3 transition-colors ${
                    index === selectedIndex
                      ? "bg-blue-50 dark:bg-blue-900/20"
                      : ""
                  } ${index === 0 ? "rounded-t-xl" : ""} ${
                    index === suggestions.length - 1 ? "rounded-b-xl" : ""
                  }`}
                >
                  <IconComponent className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">
                    {suggestion.query}
                  </span>
                  {suggestion.type === "history" && (
                    <Clock className="w-3 h-3 text-gray-400 ml-auto" />
                  )}
                  {suggestion.type === "trending" && (
                    <TrendingUp className="w-3 h-3 text-green-500 ml-auto" />
                  )}
                </button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Resultados da busca */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="mt-4 space-y-3"
          >
            {searchResults.length > 0 ? (
              searchResults.map((song) => (
                <motion.div
                  key={song.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center space-x-4">
                    <img
                      src={song.thumbnailUrl}
                      alt={song.title}
                      className="w-16 h-12 rounded-lg object-cover"
                    />

                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 dark:text-white truncate">
                        {song.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {song.artist}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-gray-500">
                          {song.formattedDuration}
                        </span>
                        {song.genre && (
                          <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                            {song.genre}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSongAction(song, "preview")}
                        className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                        title="Preview"
                      >
                        <Play className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => handleSongAction(song, "share")}
                        className="p-2 text-gray-400 hover:text-green-500 transition-colors"
                        title="Compartilhar"
                      >
                        <Share2 className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => handleSongAction(song, "suggest")}
                        className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2"
                      >
                        <Plus className="w-4 h-4" />
                        <span>Sugerir</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Music className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Nenhuma música encontrada</p>
                <p className="text-sm">Tente buscar por outro termo</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedSearch;
