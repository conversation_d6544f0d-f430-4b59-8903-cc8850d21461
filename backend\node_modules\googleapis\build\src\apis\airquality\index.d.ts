/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { airquality_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof airquality_v1.Airquality;
};
export declare function airquality(version: 'v1'): airquality_v1.Airquality;
export declare function airquality(options: airquality_v1.Options): airquality_v1.Airquality;
declare const auth: AuthPlus;
export { auth };
export { airquality_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
