/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { apim_v1alpha } from './v1alpha';
export declare const VERSIONS: {
    v1alpha: typeof apim_v1alpha.Apim;
};
export declare function apim(version: 'v1alpha'): apim_v1alpha.Apim;
export declare function apim(options: apim_v1alpha.Options): apim_v1alpha.Apim;
declare const auth: AuthPlus;
export { auth };
export { apim_v1alpha };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
