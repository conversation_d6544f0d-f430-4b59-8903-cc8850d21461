/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { biglake_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof biglake_v1.Biglake;
};
export declare function biglake(version: 'v1'): biglake_v1.Biglake;
export declare function biglake(options: biglake_v1.Options): biglake_v1.Biglake;
declare const auth: AuthPlus;
export { auth };
export { biglake_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
